#!/bin/bash

# Pet E-commerce Store V14.0 自动恢复脚本
# 使用方法: ./restore.sh

set -e  # 遇到错误时退出

echo "=== Pet E-commerce Store V14.0 自动恢复脚本 ==="
echo "开始恢复备份..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查必要文件是否存在
if [ ! -f "docker-compose.yml" ]; then
    echo "错误: 未找到docker-compose.yml文件"
    exit 1
fi

if [ ! -f "mysql-backup.sql" ]; then
    echo "错误: 未找到mysql-backup.sql数据库备份文件"
    exit 1
fi

echo "1. 停止可能存在的容器..."
docker-compose down -v 2>/dev/null || true

echo "2. 启动MySQL和Redis服务..."
docker-compose up -d mysql redis

echo "3. 等待数据库启动完成..."
sleep 30

# 检查MySQL是否启动成功
echo "4. 检查MySQL连接..."
for i in {1..10}; do
    if docker exec pet-store-mysql mysql -u root -ppet_store_root_2025 -e "SELECT 1;" &>/dev/null; then
        echo "MySQL连接成功"
        break
    fi
    if [ $i -eq 10 ]; then
        echo "错误: MySQL启动失败"
        docker-compose logs mysql
        exit 1
    fi
    echo "等待MySQL启动... ($i/10)"
    sleep 5
done

echo "5. 导入数据库备份..."
docker exec -i pet-store-mysql mysql -u root -ppet_store_root_2025 < mysql-backup.sql

echo "6. 恢复WordPress数据..."
if [ -f "wordpress-data.tar.gz" ]; then
    docker run --rm -v "$(basename $(pwd))_wordpress_data":/data -v "$(pwd)":/backup alpine tar xzf /backup/wordpress-data.tar.gz -C /data
    echo "WordPress数据恢复完成"
else
    echo "警告: 未找到wordpress-data.tar.gz文件"
fi

echo "7. 恢复Redis数据..."
if [ -f "redis-data.tar.gz" ]; then
    docker run --rm -v "$(basename $(pwd))_redis_data":/data -v "$(pwd)":/backup alpine tar xzf /backup/redis-data.tar.gz -C /data
    echo "Redis数据恢复完成"
else
    echo "警告: 未找到redis-data.tar.gz文件"
fi

echo "8. 启动WordPress和Nginx服务..."
docker-compose up -d wordpress nginx

echo "9. 等待服务启动完成..."
sleep 20

echo "10. 检查服务状态..."
docker-compose ps

echo ""
echo "=== 恢复完成! ==="
echo ""
echo "服务访问地址:"
echo "- WordPress后台: http://localhost:8080/wp-admin"
echo "- GraphQL端点: http://localhost:8080/graphql"
echo "- 前端应用: http://localhost:3000 (需要单独启动)"
echo ""
echo "启动前端服务:"
echo "docker-compose --profile frontend up -d frontend"
echo ""
echo "启动phpMyAdmin (开发环境):"
echo "docker-compose --profile dev up -d phpmyadmin"
echo "访问地址: http://localhost:8081"
echo ""
echo "默认登录信息:"
echo "- WordPress: admin / admin123"
echo "- 数据库: wpuser / pet_store_wp_2025"
echo ""
echo "如有问题，请查看 RESTORE_GUIDE.md 获取详细说明"
