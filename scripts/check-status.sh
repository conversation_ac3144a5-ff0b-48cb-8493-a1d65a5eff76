#!/bin/bash

# 宠物电商项目状态检查脚本

echo "🐾 Pet Store E-commerce - 服务状态检查"
echo "========================================"

# 检查 Docker Compose 服务状态
echo ""
echo "📦 Docker 服务状态:"
docker compose ps

echo ""
echo "🌐 服务连接测试:"

# 检查 WordPress
echo -n "WordPress: "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "302\|200"; then
    echo "✅ 运行正常 (http://localhost:8080)"
else
    echo "❌ 连接失败"
fi

# 检查 MySQL
echo -n "MySQL: "
if docker compose exec mysql mysqladmin ping -h localhost -u root -ppet_store_root_2025 --silent; then
    echo "✅ 运行正常"
else
    echo "❌ 连接失败"
fi

# 检查 Redis
echo -n "Redis: "
if docker compose exec redis redis-cli -a pet_store_redis_2025 ping | grep -q "PONG"; then
    echo "✅ 运行正常"
else
    echo "❌ 连接失败"
fi

# 检查 Nginx
echo -n "Nginx: "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health | grep -q "200"; then
    echo "✅ 运行正常"
else
    echo "⚠️  健康检查端点不可用，但服务可能正常"
fi

echo -n "GraphQL API: "
if curl -s http://localhost:8080/graphql | grep -q "GraphQL Request must include"; then
    echo "✅ 可访问"
elif curl -s http://localhost:8080/graphql | grep -q "Fatal error"; then
    echo "❌ 有错误"
else
    echo "⚠️  需要检查"
fi

echo -n "前端 (WooNuxt): "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
    echo "✅ 可访问"
else
    echo "❌ 无法访问 (可能未启动)"
fi

echo ""
echo "🔗 重要链接:"
echo "   前端 (WooNuxt): http://localhost:3000"
echo "   WordPress 前台: http://localhost:8080"
echo "   WordPress 后台: http://localhost:8080/wp-admin"
echo "   GraphQL API: http://localhost:8080/graphql"
echo "   phpMyAdmin: http://localhost:8081 (需要启动 dev profile)"
echo ""

echo "📊 资源使用情况:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo ""
echo "🔒 安全配置检查:"
echo -n "CSP 策略 (管理后台): "
if curl -s -I http://localhost:8080/wp-admin/ | grep -q "unsafe-eval"; then
    echo "✅ 已配置 (支持 WordPress 管理功能)"
else
    echo "⚠️  可能需要调整"
fi

echo -n "Web Workers 支持: "
if curl -s -I http://localhost:8080/wp-admin/ | grep -q "worker-src"; then
    echo "✅ 已配置 (支持 WooCommerce 功能)"
else
    echo "⚠️  可能需要调整"
fi

echo ""
echo "📝 下一步操作:"
echo "   1. 访问 http://localhost:8080 完成 WordPress 安装"
echo "   2. 参考 scripts/wp-manual-setup.md 进行详细配置"
echo "   3. 安装必要的插件和主题"
echo "   4. 配置 WooCommerce 电商功能"
echo ""
echo "🔧 故障排除文档:"
echo "   - scripts/wordpress-error-solutions.md (PHP 错误修复)"
echo "   - scripts/csp-fix-guide.md (CSP 策略配置)"
echo "   - scripts/sprintf-error-guide.md (sprintf 错误修复)"
echo "   - scripts/web-workers-csp-fix.md (Web Workers CSP 修复)"
echo ""
echo "🐾 宠物电商后端已准备就绪！"
