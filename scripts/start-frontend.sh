#!/bin/bash

# WooNuxt 前端启动脚本

echo "🚀 启动 WooNuxt 前端服务"
echo "========================"

# 检查后端服务是否运行
echo "🔍 检查后端服务状态..."
if ! docker compose ps wordpress | grep -q "Up"; then
    echo "⚠️  WordPress 后端未运行，正在启动..."
    docker compose up -d mysql redis wordpress nginx
    echo "⏳ 等待后端服务启动..."
    sleep 15
fi

# 检查 GraphQL 端点
echo "🔍 检查 GraphQL 端点..."
if curl -s http://localhost:8080/graphql | grep -q "GraphQL"; then
    echo "✅ GraphQL 端点正常"
else
    echo "❌ GraphQL 端点异常，请检查后端配置"
    exit 1
fi

# 检查前端依赖
echo "📦 检查前端依赖..."
if [ ! -d "frontend/node_modules" ]; then
    echo "📥 安装前端依赖..."
    cd frontend && npm install && cd ..
fi

# 启动前端服务
echo "🚀 启动前端容器..."
docker compose --profile frontend up -d frontend

# 等待服务启动
echo "⏳ 等待前端服务启动..."
sleep 10

# 检查前端服务状态
echo "🔍 检查前端服务状态..."
if curl -s http://localhost:3000 | grep -q "html\|nuxt"; then
    echo "✅ 前端服务启动成功！"
else
    echo "⚠️  前端服务可能还在启动中..."
fi

echo ""
echo "🌐 服务地址："
echo "   前端 (WooNuxt): http://localhost:3000"
echo "   后端 (WordPress): http://localhost:8080"
echo "   GraphQL API: http://localhost:8080/graphql"
echo ""
echo "📋 有用的命令："
echo "   查看前端日志: docker compose logs -f frontend"
echo "   重启前端: docker compose restart frontend"
echo "   停止前端: docker compose --profile frontend down"
echo ""
echo "🎉 WooNuxt 前端已启动！"
