#!/bin/bash

# 宠物电商全栈项目启动脚本

echo "🐾 启动宠物电商全栈项目"
echo "========================"

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 启动后端服务
echo "🚀 启动后端服务 (WordPress + MySQL + Redis + Nginx)..."
docker compose up -d mysql redis wordpress nginx

echo "⏳ 等待后端服务启动..."
sleep 15

# 检查后端服务状态
echo "🔍 检查后端服务..."
if curl -s http://localhost:8080 | grep -q "WordPress\|html"; then
    echo "✅ WordPress 后端正常"
else
    echo "❌ WordPress 后端异常"
    exit 1
fi

if curl -s http://localhost:8080/graphql | grep -q "GraphQL"; then
    echo "✅ GraphQL API 正常"
else
    echo "❌ GraphQL API 异常"
    exit 1
fi

# 检查前端依赖
echo "📦 检查前端依赖..."
if [ ! -d "frontend/node_modules" ]; then
    echo "📥 安装前端依赖..."
    cd frontend && npm install && cd ..
fi

# 启动前端服务
echo "🚀 启动前端服务 (WooNuxt)..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "⏳ 等待前端服务启动..."
sleep 10

# 检查前端服务
echo "🔍 检查前端服务..."
if curl -s http://localhost:3000 | grep -q "html\|nuxt"; then
    echo "✅ WooNuxt 前端正常"
else
    echo "⚠️  前端服务可能还在启动中..."
fi

echo ""
echo "🎉 全栈项目启动完成！"
echo ""
echo "🌐 服务地址："
echo "   前端 (WooNuxt):     http://localhost:3000"
echo "   后端 (WordPress):   http://localhost:8080"
echo "   WordPress 后台:     http://localhost:8080/wp-admin"
echo "   GraphQL API:        http://localhost:8080/graphql"
echo "   phpMyAdmin:         http://localhost:8081 (需要启动 dev profile)"
echo ""
echo "📋 有用的命令："
echo "   查看所有服务状态:   ./scripts/check-status.sh"
echo "   查看前端日志:       tail -f frontend/logs/nuxt.log"
echo "   查看后端日志:       docker compose logs -f wordpress"
echo "   停止所有服务:       docker compose down && kill $FRONTEND_PID"
echo ""
echo "💡 提示："
echo "   - 前端开发服务器支持热重载"
echo "   - 后端 WordPress 数据持久化在 Docker volumes 中"
echo "   - GraphQL API 支持 JWT 认证"
echo ""
echo "🔧 如果遇到问题："
echo "   1. 检查端口是否被占用 (3000, 8080, 3306, 6379)"
echo "   2. 确保 Docker 服务正在运行"
echo "   3. 查看错误日志进行调试"
