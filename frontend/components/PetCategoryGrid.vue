<script setup lang="ts">
// 宠物分类网格组件
const props = defineProps({
  categories: { type: Array as PropType<ProductCategory[]>, default: () => [] },
  layout: { type: String as PropType<'grid' | 'carousel'>, default: 'grid' },
  showProductCount: { type: Boolean, default: true }
});

// 宠物分类配置
const petCategoryConfig = {
  'dog-supplies': {
    icon: '🐕',
    color: 'from-amber-400 to-orange-500',
    textColor: 'text-amber-700',
    bgColor: 'bg-amber-50',
    description: '为您的狗狗提供全方位的用品'
  },
  'cat-supplies': {
    icon: '🐱',
    color: 'from-gray-400 to-slate-500',
    textColor: 'text-gray-700',
    bgColor: 'bg-gray-50',
    description: '猫咪专用的精选用品'
  },
  'bird-supplies': {
    icon: '🐦',
    color: 'from-blue-400 to-indigo-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    description: '鸟类护理和娱乐用品'
  },
  'fish-supplies': {
    icon: '🐠',
    color: 'from-cyan-400 to-teal-500',
    textColor: 'text-cyan-700',
    bgColor: 'bg-cyan-50',
    description: '水族箱和鱼类护理用品'
  },
  'pet-food': {
    icon: '🍖',
    color: 'from-green-400 to-emerald-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
    description: '营养均衡的宠物食品'
  },
  'pet-toys': {
    icon: '🎾',
    color: 'from-purple-400 to-violet-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
    description: '有趣的宠物玩具和娱乐用品'
  },
  'pet-health': {
    icon: '💊',
    color: 'from-red-400 to-pink-500',
    textColor: 'text-red-700',
    bgColor: 'bg-red-50',
    description: '宠物健康和护理用品'
  },
  'pet-accessories': {
    icon: '🎀',
    color: 'from-pink-400 to-rose-500',
    textColor: 'text-pink-700',
    bgColor: 'bg-pink-50',
    description: '时尚的宠物配饰'
  }
};

// 获取分类配置
const getCategoryConfig = (slug: string) => {
  return petCategoryConfig[slug as keyof typeof petCategoryConfig] || {
    icon: '🐾',
    color: 'from-gray-400 to-gray-500',
    textColor: 'text-gray-700',
    bgColor: 'bg-gray-50',
    description: '宠物用品'
  };
};

// 轮播相关
const currentSlide = ref(0);
const carouselRef = ref<HTMLElement>();

const nextSlide = () => {
  if (currentSlide.value < props.categories.length - 1) {
    currentSlide.value++;
  } else {
    currentSlide.value = 0;
  }
};

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else {
    currentSlide.value = props.categories.length - 1;
  }
};

// 自动轮播
let autoPlayInterval: NodeJS.Timeout;

onMounted(() => {
  if (props.layout === 'carousel') {
    autoPlayInterval = setInterval(nextSlide, 5000);
  }
});

onUnmounted(() => {
  if (autoPlayInterval) {
    clearInterval(autoPlayInterval);
  }
});
</script>

<template>
  <div class="pet-category-grid">
    <!-- 网格布局 -->
    <div v-if="layout === 'grid'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <NuxtLink
        v-for="category in categories"
        :key="category.id"
        :to="`/product-category/${category.slug}`"
        class="category-card group"
      >
        <div 
          :class="[
            'relative overflow-hidden rounded-2xl p-6 h-48 flex flex-col justify-between transition-all duration-300 group-hover:scale-105 group-hover:shadow-xl',
            `bg-gradient-to-br ${getCategoryConfig(category.slug || '').color}`
          ]"
        >
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
          <div class="absolute bottom-0 left-0 w-16 h-16 bg-white bg-opacity-10 rounded-full -ml-8 -mb-8"></div>
          
          <!-- 分类图标 -->
          <div class="text-5xl mb-2 transform group-hover:scale-110 transition-transform duration-300">
            {{ getCategoryConfig(category.slug || '').icon }}
          </div>
          
          <!-- 分类信息 -->
          <div class="text-white">
            <h3 class="text-xl font-bold mb-1">{{ category.name }}</h3>
            <p class="text-sm opacity-90 mb-2">
              {{ getCategoryConfig(category.slug || '').description }}
            </p>
            <div v-if="showProductCount && category.count" class="text-xs opacity-75">
              {{ category.count }} 件商品
            </div>
          </div>
          
          <!-- 箭头图标 -->
          <div class="absolute bottom-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Icon name="arrow-right" class="w-5 h-5" />
          </div>
        </div>
      </NuxtLink>
    </div>
    
    <!-- 轮播布局 -->
    <div v-else-if="layout === 'carousel'" class="relative">
      <div ref="carouselRef" class="overflow-hidden rounded-2xl">
        <div 
          class="flex transition-transform duration-500 ease-in-out"
          :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
        >
          <div
            v-for="category in categories"
            :key="category.id"
            class="w-full flex-shrink-0"
          >
            <NuxtLink :to="`/product-category/${category.slug}`">
              <div 
                :class="[
                  'relative h-64 md:h-80 flex items-center justify-center text-center p-8',
                  `bg-gradient-to-br ${getCategoryConfig(category.slug || '').color}`
                ]"
              >
                <!-- 背景装饰 -->
                <div class="absolute inset-0 bg-black bg-opacity-10"></div>
                <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -ml-12 -mb-12"></div>
                
                <!-- 内容 -->
                <div class="relative z-10 text-white">
                  <div class="text-8xl mb-4">
                    {{ getCategoryConfig(category.slug || '').icon }}
                  </div>
                  <h2 class="text-3xl md:text-4xl font-bold mb-2">{{ category.name }}</h2>
                  <p class="text-lg opacity-90 mb-4 max-w-md">
                    {{ getCategoryConfig(category.slug || '').description }}
                  </p>
                  <div v-if="showProductCount && category.count" class="text-sm opacity-75 mb-4">
                    {{ category.count }} 件精选商品
                  </div>
                  <button class="bg-white text-gray-800 px-6 py-3 rounded-full font-semibold hover:bg-opacity-90 transition-colors">
                    立即选购
                  </button>
                </div>
              </div>
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 轮播控制按钮 -->
      <button
        @click="prevSlide"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-colors"
      >
        <Icon name="chevron-left" class="w-6 h-6" />
      </button>
      <button
        @click="nextSlide"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-colors"
      >
        <Icon name="chevron-right" class="w-6 h-6" />
      </button>
      
      <!-- 轮播指示器 -->
      <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        <button
          v-for="(category, index) in categories"
          :key="category.id"
          @click="currentSlide = index"
          :class="[
            'w-3 h-3 rounded-full transition-colors',
            currentSlide === index ? 'bg-white' : 'bg-white bg-opacity-50'
          ]"
        ></button>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!categories.length" class="text-center py-12">
      <div class="text-6xl mb-4">🐾</div>
      <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无分类</h3>
      <p class="text-gray-500">请稍后再来查看</p>
    </div>
  </div>
</template>

<style scoped>
.category-card {
  @apply block;
}

.category-card:hover .bg-gradient-to-br {
  background-size: 110% 110%;
}

/* 轮播动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.carousel-enter-active {
  animation: slideIn 0.5s ease-out;
}
</style>
