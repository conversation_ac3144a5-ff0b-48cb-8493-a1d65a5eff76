<script setup lang="ts">
// 自定义宠物产品卡片组件
const props = defineProps({
  product: { type: Object as PropType<Product>, required: true },
  showPetInfo: { type: Boolean, default: true },
  size: { type: String as PropType<'sm' | 'md' | 'lg'>, default: 'md' }
});

// 判断产品适用的宠物类型
const petType = computed(() => {
  const categories = props.product.productCategories?.nodes || [];
  
  if (categories.some(cat => cat.slug?.includes('dog'))) return 'dog';
  if (categories.some(cat => cat.slug?.includes('cat'))) return 'cat';
  if (categories.some(cat => cat.slug?.includes('bird'))) return 'bird';
  if (categories.some(cat => cat.slug?.includes('fish'))) return 'fish';
  
  return 'general';
});

// 宠物类型配置
const petConfig = {
  dog: { icon: '🐕', color: 'text-amber-600', bgColor: 'bg-amber-50', borderColor: 'border-amber-200' },
  cat: { icon: '🐱', color: 'text-gray-600', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },
  bird: { icon: '🐦', color: 'text-blue-600', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
  fish: { icon: '🐠', color: 'text-cyan-600', bgColor: 'bg-cyan-50', borderColor: 'border-cyan-200' },
  general: { icon: '🐾', color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' }
};

const currentPetConfig = computed(() => petConfig[petType.value]);

// 尺寸配置
const sizeConfig = {
  sm: { imageSize: 200, cardClass: 'p-3', titleClass: 'text-sm', priceClass: 'text-base' },
  md: { imageSize: 280, cardClass: 'p-4', titleClass: 'text-base', priceClass: 'text-lg' },
  lg: { imageSize: 350, cardClass: 'p-6', titleClass: 'text-lg', priceClass: 'text-xl' }
};

const currentSizeConfig = computed(() => sizeConfig[props.size]);

// 产品特色标签
const productFeatures = computed(() => {
  const features = [];
  
  // 根据产品名称和描述判断特色
  const name = props.product.name?.toLowerCase() || '';
  const description = props.product.description?.toLowerCase() || '';
  
  if (name.includes('organic') || name.includes('有机') || description.includes('organic')) {
    features.push({ label: '有机', color: 'bg-green-100 text-green-700' });
  }
  
  if (name.includes('premium') || name.includes('高端') || description.includes('premium')) {
    features.push({ label: '高端', color: 'bg-purple-100 text-purple-700' });
  }
  
  if (props.product.onSale) {
    features.push({ label: '促销', color: 'bg-red-100 text-red-700' });
  }
  
  return features;
});

// 购物车相关
const { addToCart, isUpdatingCart } = useCart();
const { $toast } = useNuxtApp();

const handleAddToCart = async () => {
  try {
    await addToCart(props.product);
    $toast?.success(`${props.product.name} 已加入购物车`);
  } catch (error) {
    $toast?.error('加入购物车失败，请重试');
  }
};

// 愿望清单
const { addToWishlist, isInWishlist } = useWishlist();
const isWishlisted = computed(() => isInWishlist(props.product.id));

const toggleWishlist = () => {
  addToWishlist(props.product);
};
</script>

<template>
  <div 
    :class="[
      'pet-product-card bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group',
      currentPetConfig.borderColor,
      'border-2 border-opacity-0 hover:border-opacity-100'
    ]"
  >
    <!-- 产品图片区域 -->
    <div class="relative overflow-hidden">
      <NuxtLink :to="`/product/${product.slug}`">
        <NuxtImg
          :src="product.image?.sourceUrl || '/images/placeholder.jpg'"
          :alt="product.name"
          :width="currentSizeConfig.imageSize"
          :height="currentSizeConfig.imageSize"
          class="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </NuxtLink>
      
      <!-- 宠物类型标识 -->
      <div 
        v-if="showPetInfo"
        :class="[
          'absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium',
          currentPetConfig.bgColor,
          currentPetConfig.color
        ]"
      >
        {{ currentPetConfig.icon }} {{ petType === 'dog' ? '狗狗' : petType === 'cat' ? '猫咪' : petType === 'bird' ? '鸟类' : petType === 'fish' ? '鱼类' : '通用' }}
      </div>
      
      <!-- 愿望清单按钮 -->
      <button
        @click="toggleWishlist"
        :class="[
          'absolute top-3 right-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors',
          isWishlisted ? 'bg-red-500 text-white' : 'bg-white text-gray-400 hover:text-red-500'
        ]"
      >
        <Icon name="heart" :class="isWishlisted ? 'fill-current' : ''" />
      </button>
      
      <!-- 促销标签 -->
      <div v-if="product.onSale" class="absolute top-3 right-12 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
        SALE
      </div>
    </div>
    
    <!-- 产品信息区域 -->
    <div :class="currentSizeConfig.cardClass">
      <!-- 产品标题 -->
      <NuxtLink :to="`/product/${product.slug}`">
        <h3 :class="[
          'font-semibold text-gray-800 mb-2 line-clamp-2 hover:text-orange-600 transition-colors',
          currentSizeConfig.titleClass
        ]">
          {{ product.name }}
        </h3>
      </NuxtLink>
      
      <!-- 产品特色标签 -->
      <div v-if="productFeatures.length" class="flex flex-wrap gap-1 mb-3">
        <span
          v-for="feature in productFeatures.slice(0, 2)"
          :key="feature.label"
          :class="['px-2 py-1 text-xs rounded-full', feature.color]"
        >
          {{ feature.label }}
        </span>
      </div>
      
      <!-- 产品分类 -->
      <div class="flex flex-wrap gap-1 mb-3">
        <span
          v-for="category in product.productCategories?.nodes.slice(0, 2)"
          :key="category.id"
          class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
        >
          {{ category.name }}
        </span>
      </div>
      
      <!-- 评分 -->
      <div v-if="product.averageRating" class="flex items-center mb-3">
        <div class="flex text-yellow-400">
          <Icon v-for="i in 5" :key="i" name="star" :class="i <= product.averageRating ? 'fill-current' : ''" />
        </div>
        <span class="text-sm text-gray-500 ml-2">({{ product.reviewCount || 0 }})</span>
      </div>
      
      <!-- 价格和操作区域 -->
      <div class="flex items-center justify-between">
        <div class="price-section">
          <div v-if="product.onSale" class="flex items-center space-x-2">
            <span :class="['font-bold text-red-600', currentSizeConfig.priceClass]">
              ¥{{ product.salePrice }}
            </span>
            <span class="text-sm text-gray-500 line-through">
              ¥{{ product.regularPrice }}
            </span>
          </div>
          <div v-else :class="['font-bold text-gray-800', currentSizeConfig.priceClass]">
            ¥{{ product.price }}
          </div>
        </div>
        
        <!-- 加入购物车按钮 -->
        <button
          @click="handleAddToCart"
          :disabled="isUpdatingCart || !product.stockStatus || product.stockStatus === 'OUT_OF_STOCK'"
          :class="[
            'px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-1',
            product.stockStatus === 'OUT_OF_STOCK' 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-orange-500 text-white hover:bg-orange-600'
          ]"
        >
          <Icon v-if="isUpdatingCart" name="loading" class="animate-spin" />
          <Icon v-else name="shopping-cart" />
          <span class="hidden sm:inline">
            {{ product.stockStatus === 'OUT_OF_STOCK' ? '缺货' : '加购物车' }}
          </span>
        </button>
      </div>
      
      <!-- 库存状态 -->
      <div v-if="product.stockStatus" class="mt-2">
        <span 
          :class="[
            'text-xs px-2 py-1 rounded',
            product.stockStatus === 'IN_STOCK' ? 'bg-green-100 text-green-700' :
            product.stockStatus === 'LOW_STOCK' ? 'bg-yellow-100 text-yellow-700' :
            'bg-red-100 text-red-700'
          ]"
        >
          {{ 
            product.stockStatus === 'IN_STOCK' ? '现货充足' :
            product.stockStatus === 'LOW_STOCK' ? '库存紧张' :
            '暂时缺货'
          }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pet-product-card:hover {
  transform: translateY(-2px);
}
</style>
