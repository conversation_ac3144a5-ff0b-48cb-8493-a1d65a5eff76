
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'PetCategoryGrid': typeof import("../components/PetCategoryGrid.vue")['default']
    'PetProductCard': typeof import("../components/PetProductCard.vue")['default']
    'AccountMyDetails': typeof import("../woonuxt_base/app/components/AccountMyDetails.vue")['default']
    'CategoryCard': typeof import("../woonuxt_base/app/components/CategoryCard.vue")['default']
    'DownloadList': typeof import("../woonuxt_base/app/components/DownloadList.vue")['default']
    'DownloadableItems': typeof import("../woonuxt_base/app/components/DownloadableItems.vue")['default']
    'LangSwitcher': typeof import("../woonuxt_base/app/components/LangSwitcher.vue")['default']
    'OrderList': typeof import("../woonuxt_base/app/components/OrderList.vue")['default']
    'SignInLink': typeof import("../woonuxt_base/app/components/SignInLink.vue")['default']
    'WPAdminLink': typeof import("../woonuxt_base/app/components/WPAdminLink.vue")['default']
    'WishListItem': typeof import("../woonuxt_base/app/components/WishListItem.vue")['default']
    'CartCard': typeof import("../woonuxt_base/app/components/cartElements/CartCard.vue")['default']
    'CartTrigger': typeof import("../woonuxt_base/app/components/cartElements/CartTrigger.vue")['default']
    'CloseIcon': typeof import("../woonuxt_base/app/components/cartElements/CloseIcon.vue")['default']
    'EmptyCart': typeof import("../woonuxt_base/app/components/cartElements/EmptyCart.vue")['default']
    'QuantityInput': typeof import("../woonuxt_base/app/components/cartElements/QuantityInput.vue")['default']
    'SwipeCard': typeof import("../woonuxt_base/app/components/cartElements/SwipeCard.vue")['default']
    'TrashIcon': typeof import("../woonuxt_base/app/components/cartElements/TrashIcon.vue")['default']
    'CategoryFilter': typeof import("../woonuxt_base/app/components/filtering/CategoryFilter.vue")['default']
    'ColorFilter': typeof import("../woonuxt_base/app/components/filtering/ColorFilter.vue")['default']
    'Filters': typeof import("../woonuxt_base/app/components/filtering/Filters.vue")['default']
    'GlobalFilter': typeof import("../woonuxt_base/app/components/filtering/GlobalFilter.vue")['default']
    'OnSaleFilter': typeof import("../woonuxt_base/app/components/filtering/OnSaleFilter.vue")['default']
    'PriceFilter': typeof import("../woonuxt_base/app/components/filtering/PriceFilter.vue")['default']
    'ResetFiltersButton': typeof import("../woonuxt_base/app/components/filtering/ResetFiltersButton.vue")['default']
    'ShowFilterTrigger': typeof import("../woonuxt_base/app/components/filtering/ShowFilterTrigger.vue")['default']
    'StarRatingFilter': typeof import("../woonuxt_base/app/components/filtering/StarRatingFilter.vue")['default']
    'BillingAndShipping': typeof import("../woonuxt_base/app/components/forms/BillingAndShipping.vue")['default']
    'BillingDetails': typeof import("../woonuxt_base/app/components/forms/BillingDetails.vue")['default']
    'ChangePassword': typeof import("../woonuxt_base/app/components/forms/ChangePassword.vue")['default']
    'LoginAndRegister': typeof import("../woonuxt_base/app/components/forms/LoginAndRegister.vue")['default']
    'LoginProviders': typeof import("../woonuxt_base/app/components/forms/LoginProviders.vue")['default']
    'PasswordInput': typeof import("../woonuxt_base/app/components/forms/PasswordInput.vue")['default']
    'PersonalInformation': typeof import("../woonuxt_base/app/components/forms/PersonalInformation.vue")['default']
    'ResetPassword': typeof import("../woonuxt_base/app/components/forms/ResetPassword.vue")['default']
    'ShippingDetails': typeof import("../woonuxt_base/app/components/forms/ShippingDetails.vue")['default']
    'AppFooter': typeof import("../woonuxt_base/app/components/generalElements/AppFooter.vue")['default']
    'AppHeader': typeof import("../woonuxt_base/app/components/generalElements/AppHeader.vue")['default']
    'Breadcrumb': typeof import("../woonuxt_base/app/components/generalElements/Breadcrumb.vue")['default']
    'HeroBanner': typeof import("../woonuxt_base/app/components/generalElements/HeroBanner.vue")['default']
    'LoadingIcon': typeof import("../woonuxt_base/app/components/generalElements/LoadingIcon.vue")['default']
    'Logo': typeof import("../woonuxt_base/app/components/generalElements/Logo.vue")['default']
    'MainMenu': typeof import("../woonuxt_base/app/components/generalElements/MainMenu.vue")['default']
    'MenuTrigger': typeof import("../woonuxt_base/app/components/generalElements/MenuTrigger.vue")['default']
    'MobileMenu': typeof import("../woonuxt_base/app/components/generalElements/MobileMenu.vue")['default']
    'SEOHead': typeof import("../woonuxt_base/app/components/generalElements/SEOHead.vue")['default']
    'SearchTrigger': typeof import("../woonuxt_base/app/components/generalElements/SearchTrigger.vue")['default']
    'SocialIcons': typeof import("../woonuxt_base/app/components/generalElements/SocialIcons.vue")['default']
    'Tooltip': typeof import("../woonuxt_base/app/components/generalElements/Tooltip.vue")['default']
    'AdvancedCartDemo': typeof import("../woonuxt_base/app/components/pinia/AdvancedCartDemo.vue")['default']
    'CartButton': typeof import("../woonuxt_base/app/components/pinia/CartButton.vue")['default']
    'NotificationContainer': typeof import("../woonuxt_base/app/components/pinia/NotificationContainer.vue")['default']
    'ProductList': typeof import("../woonuxt_base/app/components/pinia/ProductList.vue")['default']
    'ReactivityDemo': typeof import("../woonuxt_base/app/components/pinia/ReactivityDemo.vue")['default']
    'SimpleCounter': typeof import("../woonuxt_base/app/components/pinia/SimpleCounter.vue")['default']
    'AddToCartButton': typeof import("../woonuxt_base/app/components/productElements/AddToCartButton.vue")['default']
    'AttributeSelections': typeof import("../woonuxt_base/app/components/productElements/AttributeSelections.vue")['default']
    'ProductCard': typeof import("../woonuxt_base/app/components/productElements/ProductCard.vue")['default']
    'ProductImageGallery': typeof import("../woonuxt_base/app/components/productElements/ProductImageGallery.vue")['default']
    'ProductPrice': typeof import("../woonuxt_base/app/components/productElements/ProductPrice.vue")['default']
    'ProductTabs': typeof import("../woonuxt_base/app/components/productElements/ProductTabs.vue")['default']
    'ReviewsScore': typeof import("../woonuxt_base/app/components/productElements/ReviewsScore.vue")['default']
    'SaleBadge': typeof import("../woonuxt_base/app/components/productElements/SaleBadge.vue")['default']
    'ShareButton': typeof import("../woonuxt_base/app/components/productElements/ShareButton.vue")['default']
    'StarRating': typeof import("../woonuxt_base/app/components/productElements/StarRating.vue")['default']
    'StockStatus': typeof import("../woonuxt_base/app/components/productElements/StockStatus.vue")['default']
    'WishlistButton': typeof import("../woonuxt_base/app/components/productElements/WishlistButton.vue")['default']
    'AddCoupon': typeof import("../woonuxt_base/app/components/shopElements/AddCoupon.vue")['default']
    'Cart': typeof import("../woonuxt_base/app/components/shopElements/Cart.vue")['default']
    'CountrySelect': typeof import("../woonuxt_base/app/components/shopElements/CountrySelect.vue")['default']
    'EmptyCartMessage': typeof import("../woonuxt_base/app/components/shopElements/EmptyCartMessage.vue")['default']
    'NoProductsFound': typeof import("../woonuxt_base/app/components/shopElements/NoProductsFound.vue")['default']
    'OrderByDropdown': typeof import("../woonuxt_base/app/components/shopElements/OrderByDropdown.vue")['default']
    'OrderStatusLabel': typeof import("../woonuxt_base/app/components/shopElements/OrderStatusLabel.vue")['default']
    'OrderSummary': typeof import("../woonuxt_base/app/components/shopElements/OrderSummary.vue")['default']
    'Pagination': typeof import("../woonuxt_base/app/components/shopElements/Pagination.vue")['default']
    'PaymentOptions': typeof import("../woonuxt_base/app/components/shopElements/PaymentOptions.vue")['default']
    'ProductGrid': typeof import("../woonuxt_base/app/components/shopElements/ProductGrid.vue")['default']
    'ProductResultCount': typeof import("../woonuxt_base/app/components/shopElements/ProductResultCount.vue")['default']
    'ProductReviews': typeof import("../woonuxt_base/app/components/shopElements/ProductReviews.vue")['default']
    'ProductRow': typeof import("../woonuxt_base/app/components/shopElements/ProductRow.vue")['default']
    'ProductSearch': typeof import("../woonuxt_base/app/components/shopElements/ProductSearch.vue")['default']
    'ShippingOptions': typeof import("../woonuxt_base/app/components/shopElements/ShippingOptions.vue")['default']
    'StateSelect': typeof import("../woonuxt_base/app/components/shopElements/StateSelect.vue")['default']
    'StripeElement': typeof import("../woonuxt_base/app/components/shopElements/StripeElement.vue")['default']
    'WebsiteShortDescription': typeof import("../woonuxt_base/app/components/shopElements/WebsiteShortDescription.vue")['default']
    'WishList': typeof import("../woonuxt_base/app/components/shopElements/WishList.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'NuxtPicture': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'Icon': typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtLinkLocale': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
    'SwitchLocalePathLink': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
      'LazyPetCategoryGrid': LazyComponent<typeof import("../components/PetCategoryGrid.vue")['default']>
    'LazyPetProductCard': LazyComponent<typeof import("../components/PetProductCard.vue")['default']>
    'LazyAccountMyDetails': LazyComponent<typeof import("../woonuxt_base/app/components/AccountMyDetails.vue")['default']>
    'LazyCategoryCard': LazyComponent<typeof import("../woonuxt_base/app/components/CategoryCard.vue")['default']>
    'LazyDownloadList': LazyComponent<typeof import("../woonuxt_base/app/components/DownloadList.vue")['default']>
    'LazyDownloadableItems': LazyComponent<typeof import("../woonuxt_base/app/components/DownloadableItems.vue")['default']>
    'LazyLangSwitcher': LazyComponent<typeof import("../woonuxt_base/app/components/LangSwitcher.vue")['default']>
    'LazyOrderList': LazyComponent<typeof import("../woonuxt_base/app/components/OrderList.vue")['default']>
    'LazySignInLink': LazyComponent<typeof import("../woonuxt_base/app/components/SignInLink.vue")['default']>
    'LazyWPAdminLink': LazyComponent<typeof import("../woonuxt_base/app/components/WPAdminLink.vue")['default']>
    'LazyWishListItem': LazyComponent<typeof import("../woonuxt_base/app/components/WishListItem.vue")['default']>
    'LazyCartCard': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/CartCard.vue")['default']>
    'LazyCartTrigger': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/CartTrigger.vue")['default']>
    'LazyCloseIcon': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/CloseIcon.vue")['default']>
    'LazyEmptyCart': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/EmptyCart.vue")['default']>
    'LazyQuantityInput': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/QuantityInput.vue")['default']>
    'LazySwipeCard': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/SwipeCard.vue")['default']>
    'LazyTrashIcon': LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/TrashIcon.vue")['default']>
    'LazyCategoryFilter': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/CategoryFilter.vue")['default']>
    'LazyColorFilter': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/ColorFilter.vue")['default']>
    'LazyFilters': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/Filters.vue")['default']>
    'LazyGlobalFilter': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/GlobalFilter.vue")['default']>
    'LazyOnSaleFilter': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/OnSaleFilter.vue")['default']>
    'LazyPriceFilter': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/PriceFilter.vue")['default']>
    'LazyResetFiltersButton': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/ResetFiltersButton.vue")['default']>
    'LazyShowFilterTrigger': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/ShowFilterTrigger.vue")['default']>
    'LazyStarRatingFilter': LazyComponent<typeof import("../woonuxt_base/app/components/filtering/StarRatingFilter.vue")['default']>
    'LazyBillingAndShipping': LazyComponent<typeof import("../woonuxt_base/app/components/forms/BillingAndShipping.vue")['default']>
    'LazyBillingDetails': LazyComponent<typeof import("../woonuxt_base/app/components/forms/BillingDetails.vue")['default']>
    'LazyChangePassword': LazyComponent<typeof import("../woonuxt_base/app/components/forms/ChangePassword.vue")['default']>
    'LazyLoginAndRegister': LazyComponent<typeof import("../woonuxt_base/app/components/forms/LoginAndRegister.vue")['default']>
    'LazyLoginProviders': LazyComponent<typeof import("../woonuxt_base/app/components/forms/LoginProviders.vue")['default']>
    'LazyPasswordInput': LazyComponent<typeof import("../woonuxt_base/app/components/forms/PasswordInput.vue")['default']>
    'LazyPersonalInformation': LazyComponent<typeof import("../woonuxt_base/app/components/forms/PersonalInformation.vue")['default']>
    'LazyResetPassword': LazyComponent<typeof import("../woonuxt_base/app/components/forms/ResetPassword.vue")['default']>
    'LazyShippingDetails': LazyComponent<typeof import("../woonuxt_base/app/components/forms/ShippingDetails.vue")['default']>
    'LazyAppFooter': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/AppFooter.vue")['default']>
    'LazyAppHeader': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/AppHeader.vue")['default']>
    'LazyBreadcrumb': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/Breadcrumb.vue")['default']>
    'LazyHeroBanner': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/HeroBanner.vue")['default']>
    'LazyLoadingIcon': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/LoadingIcon.vue")['default']>
    'LazyLogo': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/Logo.vue")['default']>
    'LazyMainMenu': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/MainMenu.vue")['default']>
    'LazyMenuTrigger': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/MenuTrigger.vue")['default']>
    'LazyMobileMenu': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/MobileMenu.vue")['default']>
    'LazySEOHead': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/SEOHead.vue")['default']>
    'LazySearchTrigger': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/SearchTrigger.vue")['default']>
    'LazySocialIcons': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/SocialIcons.vue")['default']>
    'LazyTooltip': LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/Tooltip.vue")['default']>
    'LazyAdvancedCartDemo': LazyComponent<typeof import("../woonuxt_base/app/components/pinia/AdvancedCartDemo.vue")['default']>
    'LazyCartButton': LazyComponent<typeof import("../woonuxt_base/app/components/pinia/CartButton.vue")['default']>
    'LazyNotificationContainer': LazyComponent<typeof import("../woonuxt_base/app/components/pinia/NotificationContainer.vue")['default']>
    'LazyProductList': LazyComponent<typeof import("../woonuxt_base/app/components/pinia/ProductList.vue")['default']>
    'LazyReactivityDemo': LazyComponent<typeof import("../woonuxt_base/app/components/pinia/ReactivityDemo.vue")['default']>
    'LazySimpleCounter': LazyComponent<typeof import("../woonuxt_base/app/components/pinia/SimpleCounter.vue")['default']>
    'LazyAddToCartButton': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/AddToCartButton.vue")['default']>
    'LazyAttributeSelections': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/AttributeSelections.vue")['default']>
    'LazyProductCard': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductCard.vue")['default']>
    'LazyProductImageGallery': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductImageGallery.vue")['default']>
    'LazyProductPrice': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductPrice.vue")['default']>
    'LazyProductTabs': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductTabs.vue")['default']>
    'LazyReviewsScore': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ReviewsScore.vue")['default']>
    'LazySaleBadge': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/SaleBadge.vue")['default']>
    'LazyShareButton': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ShareButton.vue")['default']>
    'LazyStarRating': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/StarRating.vue")['default']>
    'LazyStockStatus': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/StockStatus.vue")['default']>
    'LazyWishlistButton': LazyComponent<typeof import("../woonuxt_base/app/components/productElements/WishlistButton.vue")['default']>
    'LazyAddCoupon': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/AddCoupon.vue")['default']>
    'LazyCart': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/Cart.vue")['default']>
    'LazyCountrySelect': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/CountrySelect.vue")['default']>
    'LazyEmptyCartMessage': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/EmptyCartMessage.vue")['default']>
    'LazyNoProductsFound': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/NoProductsFound.vue")['default']>
    'LazyOrderByDropdown': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/OrderByDropdown.vue")['default']>
    'LazyOrderStatusLabel': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/OrderStatusLabel.vue")['default']>
    'LazyOrderSummary': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/OrderSummary.vue")['default']>
    'LazyPagination': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/Pagination.vue")['default']>
    'LazyPaymentOptions': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/PaymentOptions.vue")['default']>
    'LazyProductGrid': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductGrid.vue")['default']>
    'LazyProductResultCount': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductResultCount.vue")['default']>
    'LazyProductReviews': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductReviews.vue")['default']>
    'LazyProductRow': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductRow.vue")['default']>
    'LazyProductSearch': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductSearch.vue")['default']>
    'LazyShippingOptions': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ShippingOptions.vue")['default']>
    'LazyStateSelect': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/StateSelect.vue")['default']>
    'LazyStripeElement': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/StripeElement.vue")['default']>
    'LazyWebsiteShortDescription': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/WebsiteShortDescription.vue")['default']>
    'LazyWishList': LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/WishList.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtLinkLocale': LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
    'LazySwitchLocalePathLink': LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const PetCategoryGrid: typeof import("../components/PetCategoryGrid.vue")['default']
export const PetProductCard: typeof import("../components/PetProductCard.vue")['default']
export const AccountMyDetails: typeof import("../woonuxt_base/app/components/AccountMyDetails.vue")['default']
export const CategoryCard: typeof import("../woonuxt_base/app/components/CategoryCard.vue")['default']
export const DownloadList: typeof import("../woonuxt_base/app/components/DownloadList.vue")['default']
export const DownloadableItems: typeof import("../woonuxt_base/app/components/DownloadableItems.vue")['default']
export const LangSwitcher: typeof import("../woonuxt_base/app/components/LangSwitcher.vue")['default']
export const OrderList: typeof import("../woonuxt_base/app/components/OrderList.vue")['default']
export const SignInLink: typeof import("../woonuxt_base/app/components/SignInLink.vue")['default']
export const WPAdminLink: typeof import("../woonuxt_base/app/components/WPAdminLink.vue")['default']
export const WishListItem: typeof import("../woonuxt_base/app/components/WishListItem.vue")['default']
export const CartCard: typeof import("../woonuxt_base/app/components/cartElements/CartCard.vue")['default']
export const CartTrigger: typeof import("../woonuxt_base/app/components/cartElements/CartTrigger.vue")['default']
export const CloseIcon: typeof import("../woonuxt_base/app/components/cartElements/CloseIcon.vue")['default']
export const EmptyCart: typeof import("../woonuxt_base/app/components/cartElements/EmptyCart.vue")['default']
export const QuantityInput: typeof import("../woonuxt_base/app/components/cartElements/QuantityInput.vue")['default']
export const SwipeCard: typeof import("../woonuxt_base/app/components/cartElements/SwipeCard.vue")['default']
export const TrashIcon: typeof import("../woonuxt_base/app/components/cartElements/TrashIcon.vue")['default']
export const CategoryFilter: typeof import("../woonuxt_base/app/components/filtering/CategoryFilter.vue")['default']
export const ColorFilter: typeof import("../woonuxt_base/app/components/filtering/ColorFilter.vue")['default']
export const Filters: typeof import("../woonuxt_base/app/components/filtering/Filters.vue")['default']
export const GlobalFilter: typeof import("../woonuxt_base/app/components/filtering/GlobalFilter.vue")['default']
export const OnSaleFilter: typeof import("../woonuxt_base/app/components/filtering/OnSaleFilter.vue")['default']
export const PriceFilter: typeof import("../woonuxt_base/app/components/filtering/PriceFilter.vue")['default']
export const ResetFiltersButton: typeof import("../woonuxt_base/app/components/filtering/ResetFiltersButton.vue")['default']
export const ShowFilterTrigger: typeof import("../woonuxt_base/app/components/filtering/ShowFilterTrigger.vue")['default']
export const StarRatingFilter: typeof import("../woonuxt_base/app/components/filtering/StarRatingFilter.vue")['default']
export const BillingAndShipping: typeof import("../woonuxt_base/app/components/forms/BillingAndShipping.vue")['default']
export const BillingDetails: typeof import("../woonuxt_base/app/components/forms/BillingDetails.vue")['default']
export const ChangePassword: typeof import("../woonuxt_base/app/components/forms/ChangePassword.vue")['default']
export const LoginAndRegister: typeof import("../woonuxt_base/app/components/forms/LoginAndRegister.vue")['default']
export const LoginProviders: typeof import("../woonuxt_base/app/components/forms/LoginProviders.vue")['default']
export const PasswordInput: typeof import("../woonuxt_base/app/components/forms/PasswordInput.vue")['default']
export const PersonalInformation: typeof import("../woonuxt_base/app/components/forms/PersonalInformation.vue")['default']
export const ResetPassword: typeof import("../woonuxt_base/app/components/forms/ResetPassword.vue")['default']
export const ShippingDetails: typeof import("../woonuxt_base/app/components/forms/ShippingDetails.vue")['default']
export const AppFooter: typeof import("../woonuxt_base/app/components/generalElements/AppFooter.vue")['default']
export const AppHeader: typeof import("../woonuxt_base/app/components/generalElements/AppHeader.vue")['default']
export const Breadcrumb: typeof import("../woonuxt_base/app/components/generalElements/Breadcrumb.vue")['default']
export const HeroBanner: typeof import("../woonuxt_base/app/components/generalElements/HeroBanner.vue")['default']
export const LoadingIcon: typeof import("../woonuxt_base/app/components/generalElements/LoadingIcon.vue")['default']
export const Logo: typeof import("../woonuxt_base/app/components/generalElements/Logo.vue")['default']
export const MainMenu: typeof import("../woonuxt_base/app/components/generalElements/MainMenu.vue")['default']
export const MenuTrigger: typeof import("../woonuxt_base/app/components/generalElements/MenuTrigger.vue")['default']
export const MobileMenu: typeof import("../woonuxt_base/app/components/generalElements/MobileMenu.vue")['default']
export const SEOHead: typeof import("../woonuxt_base/app/components/generalElements/SEOHead.vue")['default']
export const SearchTrigger: typeof import("../woonuxt_base/app/components/generalElements/SearchTrigger.vue")['default']
export const SocialIcons: typeof import("../woonuxt_base/app/components/generalElements/SocialIcons.vue")['default']
export const Tooltip: typeof import("../woonuxt_base/app/components/generalElements/Tooltip.vue")['default']
export const AdvancedCartDemo: typeof import("../woonuxt_base/app/components/pinia/AdvancedCartDemo.vue")['default']
export const CartButton: typeof import("../woonuxt_base/app/components/pinia/CartButton.vue")['default']
export const NotificationContainer: typeof import("../woonuxt_base/app/components/pinia/NotificationContainer.vue")['default']
export const ProductList: typeof import("../woonuxt_base/app/components/pinia/ProductList.vue")['default']
export const ReactivityDemo: typeof import("../woonuxt_base/app/components/pinia/ReactivityDemo.vue")['default']
export const SimpleCounter: typeof import("../woonuxt_base/app/components/pinia/SimpleCounter.vue")['default']
export const AddToCartButton: typeof import("../woonuxt_base/app/components/productElements/AddToCartButton.vue")['default']
export const AttributeSelections: typeof import("../woonuxt_base/app/components/productElements/AttributeSelections.vue")['default']
export const ProductCard: typeof import("../woonuxt_base/app/components/productElements/ProductCard.vue")['default']
export const ProductImageGallery: typeof import("../woonuxt_base/app/components/productElements/ProductImageGallery.vue")['default']
export const ProductPrice: typeof import("../woonuxt_base/app/components/productElements/ProductPrice.vue")['default']
export const ProductTabs: typeof import("../woonuxt_base/app/components/productElements/ProductTabs.vue")['default']
export const ReviewsScore: typeof import("../woonuxt_base/app/components/productElements/ReviewsScore.vue")['default']
export const SaleBadge: typeof import("../woonuxt_base/app/components/productElements/SaleBadge.vue")['default']
export const ShareButton: typeof import("../woonuxt_base/app/components/productElements/ShareButton.vue")['default']
export const StarRating: typeof import("../woonuxt_base/app/components/productElements/StarRating.vue")['default']
export const StockStatus: typeof import("../woonuxt_base/app/components/productElements/StockStatus.vue")['default']
export const WishlistButton: typeof import("../woonuxt_base/app/components/productElements/WishlistButton.vue")['default']
export const AddCoupon: typeof import("../woonuxt_base/app/components/shopElements/AddCoupon.vue")['default']
export const Cart: typeof import("../woonuxt_base/app/components/shopElements/Cart.vue")['default']
export const CountrySelect: typeof import("../woonuxt_base/app/components/shopElements/CountrySelect.vue")['default']
export const EmptyCartMessage: typeof import("../woonuxt_base/app/components/shopElements/EmptyCartMessage.vue")['default']
export const NoProductsFound: typeof import("../woonuxt_base/app/components/shopElements/NoProductsFound.vue")['default']
export const OrderByDropdown: typeof import("../woonuxt_base/app/components/shopElements/OrderByDropdown.vue")['default']
export const OrderStatusLabel: typeof import("../woonuxt_base/app/components/shopElements/OrderStatusLabel.vue")['default']
export const OrderSummary: typeof import("../woonuxt_base/app/components/shopElements/OrderSummary.vue")['default']
export const Pagination: typeof import("../woonuxt_base/app/components/shopElements/Pagination.vue")['default']
export const PaymentOptions: typeof import("../woonuxt_base/app/components/shopElements/PaymentOptions.vue")['default']
export const ProductGrid: typeof import("../woonuxt_base/app/components/shopElements/ProductGrid.vue")['default']
export const ProductResultCount: typeof import("../woonuxt_base/app/components/shopElements/ProductResultCount.vue")['default']
export const ProductReviews: typeof import("../woonuxt_base/app/components/shopElements/ProductReviews.vue")['default']
export const ProductRow: typeof import("../woonuxt_base/app/components/shopElements/ProductRow.vue")['default']
export const ProductSearch: typeof import("../woonuxt_base/app/components/shopElements/ProductSearch.vue")['default']
export const ShippingOptions: typeof import("../woonuxt_base/app/components/shopElements/ShippingOptions.vue")['default']
export const StateSelect: typeof import("../woonuxt_base/app/components/shopElements/StateSelect.vue")['default']
export const StripeElement: typeof import("../woonuxt_base/app/components/shopElements/StripeElement.vue")['default']
export const WebsiteShortDescription: typeof import("../woonuxt_base/app/components/shopElements/WebsiteShortDescription.vue")['default']
export const WishList: typeof import("../woonuxt_base/app/components/shopElements/WishList.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const NuxtPicture: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const Icon: typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtLinkLocale: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
export const SwitchLocalePathLink: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyPetCategoryGrid: LazyComponent<typeof import("../components/PetCategoryGrid.vue")['default']>
export const LazyPetProductCard: LazyComponent<typeof import("../components/PetProductCard.vue")['default']>
export const LazyAccountMyDetails: LazyComponent<typeof import("../woonuxt_base/app/components/AccountMyDetails.vue")['default']>
export const LazyCategoryCard: LazyComponent<typeof import("../woonuxt_base/app/components/CategoryCard.vue")['default']>
export const LazyDownloadList: LazyComponent<typeof import("../woonuxt_base/app/components/DownloadList.vue")['default']>
export const LazyDownloadableItems: LazyComponent<typeof import("../woonuxt_base/app/components/DownloadableItems.vue")['default']>
export const LazyLangSwitcher: LazyComponent<typeof import("../woonuxt_base/app/components/LangSwitcher.vue")['default']>
export const LazyOrderList: LazyComponent<typeof import("../woonuxt_base/app/components/OrderList.vue")['default']>
export const LazySignInLink: LazyComponent<typeof import("../woonuxt_base/app/components/SignInLink.vue")['default']>
export const LazyWPAdminLink: LazyComponent<typeof import("../woonuxt_base/app/components/WPAdminLink.vue")['default']>
export const LazyWishListItem: LazyComponent<typeof import("../woonuxt_base/app/components/WishListItem.vue")['default']>
export const LazyCartCard: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/CartCard.vue")['default']>
export const LazyCartTrigger: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/CartTrigger.vue")['default']>
export const LazyCloseIcon: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/CloseIcon.vue")['default']>
export const LazyEmptyCart: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/EmptyCart.vue")['default']>
export const LazyQuantityInput: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/QuantityInput.vue")['default']>
export const LazySwipeCard: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/SwipeCard.vue")['default']>
export const LazyTrashIcon: LazyComponent<typeof import("../woonuxt_base/app/components/cartElements/TrashIcon.vue")['default']>
export const LazyCategoryFilter: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/CategoryFilter.vue")['default']>
export const LazyColorFilter: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/ColorFilter.vue")['default']>
export const LazyFilters: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/Filters.vue")['default']>
export const LazyGlobalFilter: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/GlobalFilter.vue")['default']>
export const LazyOnSaleFilter: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/OnSaleFilter.vue")['default']>
export const LazyPriceFilter: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/PriceFilter.vue")['default']>
export const LazyResetFiltersButton: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/ResetFiltersButton.vue")['default']>
export const LazyShowFilterTrigger: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/ShowFilterTrigger.vue")['default']>
export const LazyStarRatingFilter: LazyComponent<typeof import("../woonuxt_base/app/components/filtering/StarRatingFilter.vue")['default']>
export const LazyBillingAndShipping: LazyComponent<typeof import("../woonuxt_base/app/components/forms/BillingAndShipping.vue")['default']>
export const LazyBillingDetails: LazyComponent<typeof import("../woonuxt_base/app/components/forms/BillingDetails.vue")['default']>
export const LazyChangePassword: LazyComponent<typeof import("../woonuxt_base/app/components/forms/ChangePassword.vue")['default']>
export const LazyLoginAndRegister: LazyComponent<typeof import("../woonuxt_base/app/components/forms/LoginAndRegister.vue")['default']>
export const LazyLoginProviders: LazyComponent<typeof import("../woonuxt_base/app/components/forms/LoginProviders.vue")['default']>
export const LazyPasswordInput: LazyComponent<typeof import("../woonuxt_base/app/components/forms/PasswordInput.vue")['default']>
export const LazyPersonalInformation: LazyComponent<typeof import("../woonuxt_base/app/components/forms/PersonalInformation.vue")['default']>
export const LazyResetPassword: LazyComponent<typeof import("../woonuxt_base/app/components/forms/ResetPassword.vue")['default']>
export const LazyShippingDetails: LazyComponent<typeof import("../woonuxt_base/app/components/forms/ShippingDetails.vue")['default']>
export const LazyAppFooter: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/AppFooter.vue")['default']>
export const LazyAppHeader: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/AppHeader.vue")['default']>
export const LazyBreadcrumb: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/Breadcrumb.vue")['default']>
export const LazyHeroBanner: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/HeroBanner.vue")['default']>
export const LazyLoadingIcon: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/LoadingIcon.vue")['default']>
export const LazyLogo: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/Logo.vue")['default']>
export const LazyMainMenu: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/MainMenu.vue")['default']>
export const LazyMenuTrigger: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/MenuTrigger.vue")['default']>
export const LazyMobileMenu: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/MobileMenu.vue")['default']>
export const LazySEOHead: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/SEOHead.vue")['default']>
export const LazySearchTrigger: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/SearchTrigger.vue")['default']>
export const LazySocialIcons: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/SocialIcons.vue")['default']>
export const LazyTooltip: LazyComponent<typeof import("../woonuxt_base/app/components/generalElements/Tooltip.vue")['default']>
export const LazyAdvancedCartDemo: LazyComponent<typeof import("../woonuxt_base/app/components/pinia/AdvancedCartDemo.vue")['default']>
export const LazyCartButton: LazyComponent<typeof import("../woonuxt_base/app/components/pinia/CartButton.vue")['default']>
export const LazyNotificationContainer: LazyComponent<typeof import("../woonuxt_base/app/components/pinia/NotificationContainer.vue")['default']>
export const LazyProductList: LazyComponent<typeof import("../woonuxt_base/app/components/pinia/ProductList.vue")['default']>
export const LazyReactivityDemo: LazyComponent<typeof import("../woonuxt_base/app/components/pinia/ReactivityDemo.vue")['default']>
export const LazySimpleCounter: LazyComponent<typeof import("../woonuxt_base/app/components/pinia/SimpleCounter.vue")['default']>
export const LazyAddToCartButton: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/AddToCartButton.vue")['default']>
export const LazyAttributeSelections: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/AttributeSelections.vue")['default']>
export const LazyProductCard: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductCard.vue")['default']>
export const LazyProductImageGallery: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductImageGallery.vue")['default']>
export const LazyProductPrice: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductPrice.vue")['default']>
export const LazyProductTabs: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ProductTabs.vue")['default']>
export const LazyReviewsScore: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ReviewsScore.vue")['default']>
export const LazySaleBadge: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/SaleBadge.vue")['default']>
export const LazyShareButton: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/ShareButton.vue")['default']>
export const LazyStarRating: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/StarRating.vue")['default']>
export const LazyStockStatus: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/StockStatus.vue")['default']>
export const LazyWishlistButton: LazyComponent<typeof import("../woonuxt_base/app/components/productElements/WishlistButton.vue")['default']>
export const LazyAddCoupon: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/AddCoupon.vue")['default']>
export const LazyCart: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/Cart.vue")['default']>
export const LazyCountrySelect: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/CountrySelect.vue")['default']>
export const LazyEmptyCartMessage: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/EmptyCartMessage.vue")['default']>
export const LazyNoProductsFound: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/NoProductsFound.vue")['default']>
export const LazyOrderByDropdown: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/OrderByDropdown.vue")['default']>
export const LazyOrderStatusLabel: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/OrderStatusLabel.vue")['default']>
export const LazyOrderSummary: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/OrderSummary.vue")['default']>
export const LazyPagination: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/Pagination.vue")['default']>
export const LazyPaymentOptions: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/PaymentOptions.vue")['default']>
export const LazyProductGrid: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductGrid.vue")['default']>
export const LazyProductResultCount: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductResultCount.vue")['default']>
export const LazyProductReviews: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductReviews.vue")['default']>
export const LazyProductRow: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductRow.vue")['default']>
export const LazyProductSearch: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ProductSearch.vue")['default']>
export const LazyShippingOptions: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/ShippingOptions.vue")['default']>
export const LazyStateSelect: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/StateSelect.vue")['default']>
export const LazyStripeElement: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/StripeElement.vue")['default']>
export const LazyWebsiteShortDescription: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/WebsiteShortDescription.vue")['default']>
export const LazyWishList: LazyComponent<typeof import("../woonuxt_base/app/components/shopElements/WishList.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtLinkLocale: LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
export const LazySwitchLocalePathLink: LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>

export const componentNames: string[]
