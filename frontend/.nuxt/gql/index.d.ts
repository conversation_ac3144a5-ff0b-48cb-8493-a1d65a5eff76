import { getSdk as defaultGqlSdk } from '#gql/default'
export { AddCartItemsInput, AddFeeInput, AddMethodToShippingZoneInput, AddToCartInput, ApplyCouponInput, CartItemInput, CartItemQuantityInput, CartItemToProductConnectionWhereArgs, CartToCartItemConnectionWhereArgs, CategoryToCategoryConnectionWhereArgs, CategoryToContentNodeConnectionWhereArgs, CategoryToPostConnectionWhereArgs, CheckoutInput, ClearShippingZoneLocationsInput, CollectionStatsQueryInput, CollectionStatsWhereArgs, CommentToCommentConnectionWhereArgs, CommentToParentCommentConnectionWhereArgs, ContentTypeToContentNodeConnectionWhereArgs, CouponToCustomerConnectionWhereArgs, CouponToExcludedProductCategoriesConnectionWhereArgs, CouponToExcludedProductsConnectionWhereArgs, CouponToProductCategoryConnectionWhereArgs, CouponToProductUnionConnectionWhereArgs, CreateAccountInput, CreateCategoryInput, CreateCommentInput, CreateCouponInput, CreateMediaItemInput, CreateOrderInput, CreatePaColorInput, CreatePaPetTypeInput, CreatePageInput, CreatePostFormatInput, CreatePostInput, CreateProductCategoryInput, CreateProductInput, CreateProductTagInput, CreateProductTypeInput, CreateProductVariationInput, CreateShippingClassInput, CreateShippingZoneInput, CreateTagInput, CreateTaxClassInput, CreateTaxRateInput, CreateUserInput, CreateVisibleProductInput, CustomerAddressInput, CustomerToDownloadableItemConnectionWhereArgs, CustomerToOrderConnectionWhereArgs, CustomerToRefundConnectionWhereArgs, DateInput, DateQueryInput, DeleteCategoryInput, DeleteCommentInput, DeleteCouponInput, DeleteMediaItemInput, DeleteOrderInput, DeleteOrderItemsInput, DeletePaColorInput, DeletePaPetTypeInput, DeletePageInput, DeletePaymentMethodInput, DeletePostFormatInput, DeletePostInput, DeleteProductCategoryInput, DeleteProductInput, DeleteProductTagInput, DeleteProductTypeInput, DeleteProductVariationInput, DeleteReviewInput, DeleteShippingClassInput, DeleteShippingZoneInput, DeleteTagInput, DeleteTaxClassInput, DeleteTaxRateInput, DeleteUserInput, DeleteVisibleProductInput, EmptyCartInput, FeeLineInput, FillCartInput, ForgetSessionInput, GlobalProductAttributeToTermNodeConnectionWhereArgs, GroupProductToProductUnionConnectionWhereArgs, HierarchicalContentNodeToContentNodeAncestorsConnectionWhereArgs, HierarchicalContentNodeToContentNodeChildrenConnectionWhereArgs, LineItemInput, LineItemToProductConnectionWhereArgs, LinkUserIdentityInput, LoginInput, LoginWithCookiesInput, LogoutInput, MediaItemToCommentConnectionWhereArgs, MenuItemToMenuItemConnectionWhereArgs, MenuToMenuItemConnectionWhereArgs, MetaDataInput, OAuthProviderResponseInput, OrderToCommentConnectionWhereArgs, OrderToDownloadableItemConnectionWhereArgs, OrderToRefundConnectionWhereArgs, OrdersOrderbyInput, PaColorToContentNodeConnectionWhereArgs, PaColorToProductConnectionWhereArgs, PaColorToProductVariationConnectionWhereArgs, PaPetTypeToContentNodeConnectionWhereArgs, PaPetTypeToProductConnectionWhereArgs, PaPetTypeToProductVariationConnectionWhereArgs, PageToCommentConnectionWhereArgs, PageToRevisionConnectionWhereArgs, PasswordProviderResponseInput, PostCategoriesInput, PostCategoriesNodeInput, PostFormatToContentNodeConnectionWhereArgs, PostFormatToPostConnectionWhereArgs, PostObjectsConnectionOrderbyInput, PostPostFormatsInput, PostPostFormatsNodeInput, PostTagsInput, PostTagsNodeInput, PostToCategoryConnectionWhereArgs, PostToCommentConnectionWhereArgs, PostToPostFormatConnectionWhereArgs, PostToRevisionConnectionWhereArgs, PostToTagConnectionWhereArgs, PostToTermNodeConnectionWhereArgs, PostTypeOrderbyInput, ProductAllPaColorInput, ProductAllPaColorNodeInput, ProductAllPaPetTypeInput, ProductAllPaPetTypeNodeInput, ProductAttributeFilterInput, ProductAttributeInput, ProductAttributeQueryInput, ProductCategoryToContentNodeConnectionWhereArgs, ProductCategoryToProductCategoryConnectionWhereArgs, ProductCategoryToProductConnectionWhereArgs, ProductProductCategoriesInput, ProductProductCategoriesNodeInput, ProductProductTagsInput, ProductProductTagsNodeInput, ProductProductTypesInput, ProductProductTypesNodeInput, ProductShippingClassesInput, ProductShippingClassesNodeInput, ProductTagToContentNodeConnectionWhereArgs, ProductTagToProductConnectionWhereArgs, ProductTaxonomyFilterInput, ProductTaxonomyInput, ProductToCommentConnectionWhereArgs, ProductToCommentsConnectionWhereArgs, ProductToMediaItemConnectionWhereArgs, ProductToPaColorConnectionWhereArgs, ProductToPaPetTypeConnectionWhereArgs, ProductToPreviewConnectionWhereArgs, ProductToProductCategoryConnectionWhereArgs, ProductToProductConnectionWhereArgs, ProductToProductTagConnectionWhereArgs, ProductToProductTypeConnectionWhereArgs, ProductToProductUnionConnectionWhereArgs, ProductToShippingClassConnectionWhereArgs, ProductToTermNodeConnectionWhereArgs, ProductToUpsellConnectionWhereArgs, ProductToVisibleProductConnectionWhereArgs, ProductTypeToContentNodeConnectionWhereArgs, ProductTypeToProductConnectionWhereArgs, ProductVariationShippingClassesInput, ProductVariationShippingClassesNodeInput, ProductVariationVisibleProductsInput, ProductVariationVisibleProductsNodeInput, ProductVisibleProductsInput, ProductVisibleProductsNodeInput, ProductWithAttributesToProductAttributeConnectionWhereArgs, ProductWithVariationsToProductVariationConnectionWhereArgs, ProductsOrderbyInput, RefreshJwtAuthTokenInput, RefreshTokenInput, RefreshUserSecretInput, RegisterCustomerInput, RegisterUserInput, RemoveCouponsInput, RemoveItemsFromCartInput, RemoveMethodFromShippingZoneInput, ResetUserPasswordInput, RestoreCartItemsInput, RestoreCommentInput, RestoreReviewInput, RevokeUserSecretInput, RootQueryToCategoryConnectionWhereArgs, RootQueryToCommentConnectionWhereArgs, RootQueryToContentNodeConnectionWhereArgs, RootQueryToCouponConnectionWhereArgs, RootQueryToCustomerConnectionWhereArgs, RootQueryToMediaItemConnectionWhereArgs, RootQueryToMenuConnectionWhereArgs, RootQueryToMenuItemConnectionWhereArgs, RootQueryToOrderConnectionWhereArgs, RootQueryToPaColorConnectionWhereArgs, RootQueryToPaPetTypeConnectionWhereArgs, RootQueryToPageConnectionWhereArgs, RootQueryToPaymentGatewayConnectionWhereArgs, RootQueryToPluginConnectionWhereArgs, RootQueryToPostConnectionWhereArgs, RootQueryToPostFormatConnectionWhereArgs, RootQueryToProductCategoryConnectionWhereArgs, RootQueryToProductTagConnectionWhereArgs, RootQueryToProductTypeConnectionWhereArgs, RootQueryToProductUnionConnectionWhereArgs, RootQueryToRefundConnectionWhereArgs, RootQueryToRevisionsConnectionWhereArgs, RootQueryToShippingClassConnectionWhereArgs, RootQueryToTagConnectionWhereArgs, RootQueryToTaxRateConnectionWhereArgs, RootQueryToTermNodeConnectionWhereArgs, RootQueryToUserConnectionWhereArgs, RootQueryToVisibleProductConnectionWhereArgs, SendPasswordResetEmailInput, SetDefaultPaymentMethodInput, ShippingClassToContentNodeConnectionWhereArgs, ShippingClassToProductConnectionWhereArgs, ShippingClassToProductVariationConnectionWhereArgs, ShippingLineInput, ShippingLocationInput, SimpleProductToProductUnionConnectionWhereArgs, TagToContentNodeConnectionWhereArgs, TagToPostConnectionWhereArgs, TaxRateConnectionOrderbyInput, UpdateCategoryInput, UpdateCommentInput, UpdateCouponInput, UpdateCustomerInput, UpdateItemQuantitiesInput, UpdateMediaItemInput, UpdateMethodOnShippingZoneInput, UpdateOrderInput, UpdatePaColorInput, UpdatePaPetTypeInput, UpdatePageInput, UpdatePostFormatInput, UpdatePostInput, UpdateProductCategoryInput, UpdateProductInput, UpdateProductTagInput, UpdateProductTypeInput, UpdateProductVariationInput, UpdateReviewInput, UpdateSessionInput, UpdateSettingsInput, UpdateShippingClassInput, UpdateShippingMethodInput, UpdateShippingZoneInput, UpdateShippingZoneLocationsInput, UpdateTagInput, UpdateTaxRateInput, UpdateUserInput, UpdateVisibleProductInput, UserToCommentConnectionWhereArgs, UserToMediaItemConnectionWhereArgs, UserToPageConnectionWhereArgs, UserToPostConnectionWhereArgs, UserToRevisionsConnectionWhereArgs, UsersConnectionOrderbyInput, VariableProductToProductUnionConnectionWhereArgs, VisibleProductToContentNodeConnectionWhereArgs, VisibleProductToProductConnectionWhereArgs, VisibleProductToProductVariationConnectionWhereArgs, WcSettingInput, WriteReviewInput, AddToCartMutationVariables, AddToCartMutation, ApplyCouponMutationVariables, ApplyCouponMutation, ChangeShippingCountyMutationVariables, ChangeShippingCountyMutation, ChangeShippingMethodMutationVariables, ChangeShippingMethodMutation, CheckoutMutationVariables, CheckoutMutation, EmptyCartMutationVariables, EmptyCartMutation, CartFragment, CategoryImageFragment, ProductCategoryFragment, CommentFragment, CustomerFragment, AddressFragment, DownloadableItemFragment, ExternalProductFragment, ImageFragment, LineItemProductFragment, LineItemVariationFragment, LineItemFragment, LoginClientFragment, OrderFragmentFragment, PaymentGatewayFragment, ProductCategoriesFragment, ProductPricingFragment, ProductStockFragment, ProductVariationFragment, SimpleProductFragment, TermsFragment, VariableProductFragment, VariationAttributeFragment, ViewerFragment, GetAllTermsQueryVariables, GetAllTermsQuery, GetAllowedCountriesQueryVariables, GetAllowedCountriesQuery, GetCartQueryVariables, GetCartQuery, GetDownloadsQueryVariables, GetDownloadsQuery, GetOrderQueryVariables, GetOrderQuery, GetOrdersQueryVariables, GetOrdersQuery, GetProductQueryVariables, GetProductQuery, ProductWithAttributesFragment, ProductAttributeFragment, GetProductCategoriesQueryVariables, GetProductCategoriesQuery, GetProductsQueryVariables, GetProductsQuery, GetStatesQueryVariables, GetStatesQuery, GetStockStatusQueryVariables, GetStockStatusQuery, GetStripePaymentIntentQueryVariables, GetStripePaymentIntentQuery, LoginMutationVariables, LoginMutation, LoginWithProviderMutationVariables, LoginWithProviderMutation, LogoutPlaceholderQueryVariables, LogoutPlaceholderQuery, RegisterCustomerMutationVariables, RegisterCustomerMutation, RemoveCouponsMutationVariables, RemoveCouponsMutation, ResetPasswordEmailMutationVariables, ResetPasswordEmailMutation, ResetPasswordKeyMutationVariables, ResetPasswordKeyMutation, UpDateCartQuantityMutationVariables, UpDateCartQuantityMutation, UpdateCustomerMutationVariables, UpdateCustomerMutation, UpdatePasswordMutationVariables, UpdatePasswordMutation, WriteReviewMutationVariables, WriteReviewMutation } from "#gql/default";
declare module '#gql' {
  type GqlClients = 'default'
  type GqlOps = 'addToCart' | 'applyCoupon' | 'ChangeShippingCounty' | 'ChangeShippingMethod' | 'Checkout' | 'EmptyCart' | 'Cart' | 'CategoryImage' | 'ProductCategory' | 'Comment' | 'Customer' | 'Address' | 'DownloadableItem' | 'ExternalProduct' | 'Image' | 'LineItemProduct' | 'LineItemVariation' | 'LineItem' | 'LoginClient' | 'OrderFragment' | 'PaymentGateway' | 'ProductCategories' | 'ProductPricing' | 'ProductStock' | 'ProductVariation' | 'SimpleProduct' | 'Terms' | 'VariableProduct' | 'VariationAttribute' | 'Viewer' | 'getAllTerms' | 'getAllowedCountries' | 'getCart' | 'getDownloads' | 'getOrder' | 'getOrders' | 'getProduct' | 'ProductWithAttributes' | 'ProductAttribute' | 'getProductCategories' | 'getProducts' | 'getStates' | 'getStockStatus' | 'getStripePaymentIntent' | 'login' | 'loginWithProvider' | 'LogoutPlaceholder' | 'registerCustomer' | 'removeCoupons' | 'ResetPasswordEmail' | 'ResetPasswordKey' | 'UpDateCartQuantity' | 'UpdateCustomer' | 'updatePassword' | 'WriteReview'
  const GqClientOps = {"default":["addToCart","applyCoupon","ChangeShippingCounty","ChangeShippingMethod","Checkout","EmptyCart","Cart","CategoryImage","ProductCategory","Comment","Customer","Address","DownloadableItem","ExternalProduct","Image","LineItemProduct","LineItemVariation","LineItem","LoginClient","OrderFragment","PaymentGateway","ProductCategories","ProductPricing","ProductStock","ProductVariation","SimpleProduct","Terms","VariableProduct","VariationAttribute","Viewer","getAllTerms","getAllowedCountries","getCart","getDownloads","getOrder","getOrders","getProduct","ProductWithAttributes","ProductAttribute","getProductCategories","getProducts","getStates","getStockStatus","getStripePaymentIntent","login","loginWithProvider","LogoutPlaceholder","registerCustomer","removeCoupons","ResetPasswordEmail","ResetPasswordKey","UpDateCartQuantity","UpdateCustomer","updatePassword","WriteReview"]}
  const GqlSdks = {
    default: defaultGqlSdk,
  }
  export const GqlChangeShippingCounty: (...params: Parameters<GqlSdkFuncs['ChangeShippingCounty']>) => ReturnType<GqlSdkFuncs['ChangeShippingCounty']>
  export const GqlChangeShippingMethod: (...params: Parameters<GqlSdkFuncs['ChangeShippingMethod']>) => ReturnType<GqlSdkFuncs['ChangeShippingMethod']>
  export const GqlCheckout: (...params: Parameters<GqlSdkFuncs['Checkout']>) => ReturnType<GqlSdkFuncs['Checkout']>
  export const GqlEmptyCart: (...params: Parameters<GqlSdkFuncs['EmptyCart']>) => ReturnType<GqlSdkFuncs['EmptyCart']>
  export const GqlLogoutPlaceholder: (...params: Parameters<GqlSdkFuncs['LogoutPlaceholder']>) => ReturnType<GqlSdkFuncs['LogoutPlaceholder']>
  export const GqlResetPasswordEmail: (...params: Parameters<GqlSdkFuncs['ResetPasswordEmail']>) => ReturnType<GqlSdkFuncs['ResetPasswordEmail']>
  export const GqlResetPasswordKey: (...params: Parameters<GqlSdkFuncs['ResetPasswordKey']>) => ReturnType<GqlSdkFuncs['ResetPasswordKey']>
  export const GqlUpDateCartQuantity: (...params: Parameters<GqlSdkFuncs['UpDateCartQuantity']>) => ReturnType<GqlSdkFuncs['UpDateCartQuantity']>
  export const GqlUpdateCustomer: (...params: Parameters<GqlSdkFuncs['UpdateCustomer']>) => ReturnType<GqlSdkFuncs['UpdateCustomer']>
  export const GqlWriteReview: (...params: Parameters<GqlSdkFuncs['WriteReview']>) => ReturnType<GqlSdkFuncs['WriteReview']>
  export const GqlAddToCart: (...params: Parameters<GqlSdkFuncs['addToCart']>) => ReturnType<GqlSdkFuncs['addToCart']>
  export const GqlApplyCoupon: (...params: Parameters<GqlSdkFuncs['applyCoupon']>) => ReturnType<GqlSdkFuncs['applyCoupon']>
  export const GqlGetAllTerms: (...params: Parameters<GqlSdkFuncs['getAllTerms']>) => ReturnType<GqlSdkFuncs['getAllTerms']>
  export const GqlGetAllowedCountries: (...params: Parameters<GqlSdkFuncs['getAllowedCountries']>) => ReturnType<GqlSdkFuncs['getAllowedCountries']>
  export const GqlGetCart: (...params: Parameters<GqlSdkFuncs['getCart']>) => ReturnType<GqlSdkFuncs['getCart']>
  export const GqlGetDownloads: (...params: Parameters<GqlSdkFuncs['getDownloads']>) => ReturnType<GqlSdkFuncs['getDownloads']>
  export const GqlGetOrder: (...params: Parameters<GqlSdkFuncs['getOrder']>) => ReturnType<GqlSdkFuncs['getOrder']>
  export const GqlGetOrders: (...params: Parameters<GqlSdkFuncs['getOrders']>) => ReturnType<GqlSdkFuncs['getOrders']>
  export const GqlGetProduct: (...params: Parameters<GqlSdkFuncs['getProduct']>) => ReturnType<GqlSdkFuncs['getProduct']>
  export const GqlGetProductCategories: (...params: Parameters<GqlSdkFuncs['getProductCategories']>) => ReturnType<GqlSdkFuncs['getProductCategories']>
  export const GqlGetProducts: (...params: Parameters<GqlSdkFuncs['getProducts']>) => ReturnType<GqlSdkFuncs['getProducts']>
  export const GqlGetStates: (...params: Parameters<GqlSdkFuncs['getStates']>) => ReturnType<GqlSdkFuncs['getStates']>
  export const GqlGetStockStatus: (...params: Parameters<GqlSdkFuncs['getStockStatus']>) => ReturnType<GqlSdkFuncs['getStockStatus']>
  export const GqlGetStripePaymentIntent: (...params: Parameters<GqlSdkFuncs['getStripePaymentIntent']>) => ReturnType<GqlSdkFuncs['getStripePaymentIntent']>
  export const GqlLogin: (...params: Parameters<GqlSdkFuncs['login']>) => ReturnType<GqlSdkFuncs['login']>
  export const GqlLoginWithProvider: (...params: Parameters<GqlSdkFuncs['loginWithProvider']>) => ReturnType<GqlSdkFuncs['loginWithProvider']>
  export const GqlRegisterCustomer: (...params: Parameters<GqlSdkFuncs['registerCustomer']>) => ReturnType<GqlSdkFuncs['registerCustomer']>
  export const GqlRemoveCoupons: (...params: Parameters<GqlSdkFuncs['removeCoupons']>) => ReturnType<GqlSdkFuncs['removeCoupons']>
  export const GqlUpdatePassword: (...params: Parameters<GqlSdkFuncs['updatePassword']>) => ReturnType<GqlSdkFuncs['updatePassword']>
  type GqlSdkFuncs = ReturnType<typeof defaultGqlSdk>
}