// Generated by auto imports
export {}
declare global {
  const GqlAddToCart: typeof import('../gql')['GqlAddToCart']
  const GqlApplyCoupon: typeof import('../gql')['GqlApplyCoupon']
  const GqlChangeShippingCounty: typeof import('../gql')['GqlChangeShippingCounty']
  const GqlChangeShippingMethod: typeof import('../gql')['GqlChangeShippingMethod']
  const GqlCheckout: typeof import('../gql')['GqlCheckout']
  const GqlEmptyCart: typeof import('../gql')['GqlEmptyCart']
  const GqlGetAllTerms: typeof import('../gql')['GqlGetAllTerms']
  const GqlGetAllowedCountries: typeof import('../gql')['GqlGetAllowedCountries']
  const GqlGetCart: typeof import('../gql')['GqlGetCart']
  const GqlGetDownloads: typeof import('../gql')['GqlGetDownloads']
  const GqlGetOrder: typeof import('../gql')['GqlGetOrder']
  const GqlGetOrders: typeof import('../gql')['GqlGetOrders']
  const GqlGetProduct: typeof import('../gql')['GqlGetProduct']
  const GqlGetProductCategories: typeof import('../gql')['GqlGetProductCategories']
  const GqlGetProducts: typeof import('../gql')['GqlGetProducts']
  const GqlGetStates: typeof import('../gql')['GqlGetStates']
  const GqlGetStockStatus: typeof import('../gql')['GqlGetStockStatus']
  const GqlGetStripePaymentIntent: typeof import('../gql')['GqlGetStripePaymentIntent']
  const GqlLogin: typeof import('../gql')['GqlLogin']
  const GqlLoginWithProvider: typeof import('../gql')['GqlLoginWithProvider']
  const GqlLogoutPlaceholder: typeof import('../gql')['GqlLogoutPlaceholder']
  const GqlRegisterCustomer: typeof import('../gql')['GqlRegisterCustomer']
  const GqlRemoveCoupons: typeof import('../gql')['GqlRemoveCoupons']
  const GqlResetPasswordEmail: typeof import('../gql')['GqlResetPasswordEmail']
  const GqlResetPasswordKey: typeof import('../gql')['GqlResetPasswordKey']
  const GqlUpDateCartQuantity: typeof import('../gql')['GqlUpDateCartQuantity']
  const GqlUpdateCustomer: typeof import('../gql')['GqlUpdateCustomer']
  const GqlUpdatePassword: typeof import('../gql')['GqlUpdatePassword']
  const GqlWriteReview: typeof import('../gql')['GqlWriteReview']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const callOnce: typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const clearError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const computed: typeof import('../../node_modules/vue')['computed']
  const createError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']
  const customRef: typeof import('../../node_modules/vue')['customRef']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('../../node_modules/vue')['defineAsyncComponent']
  const defineComponent: typeof import('../../node_modules/vue')['defineComponent']
  const defineI18nConfig: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nConfig']
  const defineI18nLocale: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nLocale']
  const defineI18nRoute: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nRoute']
  const defineLazyHydrationComponent: typeof import('../../node_modules/nuxt/dist/app/composables/lazy-hydration')['defineLazyHydrationComponent']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const effect: typeof import('../../node_modules/vue')['effect']
  const effectScope: typeof import('../../node_modules/vue')['effectScope']
  const getAppManifest: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getCurrentInstance: typeof import('../../node_modules/vue')['getCurrentInstance']
  const getCurrentScope: typeof import('../../node_modules/vue')['getCurrentScope']
  const getRouteRules: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const h: typeof import('../../node_modules/vue')['h']
  const hasInjectionContext: typeof import('../../node_modules/vue')['hasInjectionContext']
  const inject: typeof import('../../node_modules/vue')['inject']
  const injectHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('../../node_modules/vue')['isProxy']
  const isReactive: typeof import('../../node_modules/vue')['isReactive']
  const isReadonly: typeof import('../../node_modules/vue')['isReadonly']
  const isRef: typeof import('../../node_modules/vue')['isRef']
  const isShallow: typeof import('../../node_modules/vue')['isShallow']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const markRaw: typeof import('../../node_modules/vue')['markRaw']
  const mergeModels: typeof import('../../node_modules/vue')['mergeModels']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('../../node_modules/vue')['nextTick']
  const onActivated: typeof import('../../node_modules/vue')['onActivated']
  const onBeforeMount: typeof import('../../node_modules/vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('../../node_modules/vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('../../node_modules/vue')['onBeforeUpdate']
  const onDeactivated: typeof import('../../node_modules/vue')['onDeactivated']
  const onErrorCaptured: typeof import('../../node_modules/vue')['onErrorCaptured']
  const onMounted: typeof import('../../node_modules/vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('../../node_modules/vue')['onRenderTracked']
  const onRenderTriggered: typeof import('../../node_modules/vue')['onRenderTriggered']
  const onScopeDispose: typeof import('../../node_modules/vue')['onScopeDispose']
  const onServerPrefetch: typeof import('../../node_modules/vue')['onServerPrefetch']
  const onUnmounted: typeof import('../../node_modules/vue')['onUnmounted']
  const onUpdated: typeof import('../../node_modules/vue')['onUpdated']
  const onWatcherCleanup: typeof import('../../node_modules/vue')['onWatcherCleanup']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('../../node_modules/vue')['provide']
  const proxyRefs: typeof import('../../node_modules/vue')['proxyRefs']
  const reactive: typeof import('../../node_modules/vue')['reactive']
  const readonly: typeof import('../../node_modules/vue')['readonly']
  const ref: typeof import('../../node_modules/vue')['ref']
  const refreshCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const requestIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('../../node_modules/vue')['resolveComponent']
  const setInterval: typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('../../node_modules/vue')['shallowReactive']
  const shallowReadonly: typeof import('../../node_modules/vue')['shallowReadonly']
  const shallowRef: typeof import('../../node_modules/vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']
  const toRaw: typeof import('../../node_modules/vue')['toRaw']
  const toRef: typeof import('../../node_modules/vue')['toRef']
  const toRefs: typeof import('../../node_modules/vue')['toRefs']
  const toValue: typeof import('../../node_modules/vue')['toValue']
  const triggerRef: typeof import('../../node_modules/vue')['triggerRef']
  const tryUseNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('../../node_modules/vue')['unref']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAsyncGql: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useAsyncGql']
  const useAttrs: typeof import('../../node_modules/vue')['useAttrs']
  const useAuth: typeof import('../../woonuxt_base/app/composables/useAuth')['useAuth']
  const useBrowserLocale: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useBrowserLocale']
  const useCart: typeof import('../../woonuxt_base/app/composables/useCart')['useCart']
  const useCheckout: typeof import('../../woonuxt_base/app/composables/useCheckout')['useCheckout']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCookieLocale: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useCookieLocale']
  const useCountry: typeof import('../../woonuxt_base/app/composables/useCountry')['useCountry']
  const useCssModule: typeof import('../../node_modules/vue')['useCssModule']
  const useCssVars: typeof import('../../node_modules/vue')['useCssVars']
  const useError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFiltering: typeof import('../../woonuxt_base/app/composables/useFiltering')['useFiltering']
  const useGql: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGql']
  const useGqlCors: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlCors']
  const useGqlError: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlError']
  const useGqlHeaders: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlHeaders']
  const useGqlHost: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlHost']
  const useGqlToken: typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlToken']
  const useHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']
  const useHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']
  const useHelpers: typeof import('../../woonuxt_base/app/composables/useHelpers')['useHelpers']
  const useHydration: typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useI18n: typeof import('../../node_modules/vue-i18n/dist/vue-i18n')['useI18n']
  const useId: typeof import('../../node_modules/vue')['useId']
  const useImage: typeof import('../../node_modules/@nuxt/image/dist/runtime/composables')['useImage']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('../../node_modules/vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useLocaleHead: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleHead']
  const useLocalePath: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocalePath']
  const useLocaleRoute: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleRoute']
  const useModel: typeof import('../../node_modules/vue')['useModel']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useNuxtDevTools: typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']
  const usePreviewMode: typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const useProducts: typeof import('../../woonuxt_base/app/composables/useProducts')['useProducts']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResponseHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouteBaseName: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useRouteBaseName']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useScript: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptRybbitAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']
  const useScriptSegment: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptSnapchatPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']
  const useScriptStripe: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTriggerConsent: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptUmamiAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']
  const useScriptVimeoPlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useSearching: typeof import('../../woonuxt_base/app/composables/useSearch')['useSearching']
  const useSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']
  const useServerHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']
  const useServerHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']
  const useSetI18nParams: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSetI18nParams']
  const useShadowRoot: typeof import('../../node_modules/vue')['useShadowRoot']
  const useSlots: typeof import('../../node_modules/vue')['useSlots']
  const useSorting: typeof import('../../woonuxt_base/app/composables/useSorting')['useSorting']
  const useState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']
  const useSwitchLocalePath: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSwitchLocalePath']
  const useTemplateRef: typeof import('../../node_modules/vue')['useTemplateRef']
  const useTransitionState: typeof import('../../node_modules/vue')['useTransitionState']
  const useWishlist: typeof import('../../woonuxt_base/app/composables/useWishlist')['useWishlist']
  const watch: typeof import('../../node_modules/vue')['watch']
  const watchEffect: typeof import('../../node_modules/vue')['watchEffect']
  const watchPostEffect: typeof import('../../node_modules/vue')['watchPostEffect']
  const watchSyncEffect: typeof import('../../node_modules/vue')['watchSyncEffect']
  const withCtx: typeof import('../../node_modules/vue')['withCtx']
  const withDirectives: typeof import('../../node_modules/vue')['withDirectives']
  const withKeys: typeof import('../../node_modules/vue')['withKeys']
  const withMemo: typeof import('../../node_modules/vue')['withMemo']
  const withModifiers: typeof import('../../node_modules/vue')['withModifiers']
  const withScopeId: typeof import('../../node_modules/vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from '../../node_modules/vue'
  import('../../node_modules/vue')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly GqlAddToCart: UnwrapRef<typeof import('../gql')['GqlAddToCart']>
    readonly GqlApplyCoupon: UnwrapRef<typeof import('../gql')['GqlApplyCoupon']>
    readonly GqlChangeShippingCounty: UnwrapRef<typeof import('../gql')['GqlChangeShippingCounty']>
    readonly GqlChangeShippingMethod: UnwrapRef<typeof import('../gql')['GqlChangeShippingMethod']>
    readonly GqlCheckout: UnwrapRef<typeof import('../gql')['GqlCheckout']>
    readonly GqlEmptyCart: UnwrapRef<typeof import('../gql')['GqlEmptyCart']>
    readonly GqlGetAllTerms: UnwrapRef<typeof import('../gql')['GqlGetAllTerms']>
    readonly GqlGetAllowedCountries: UnwrapRef<typeof import('../gql')['GqlGetAllowedCountries']>
    readonly GqlGetCart: UnwrapRef<typeof import('../gql')['GqlGetCart']>
    readonly GqlGetDownloads: UnwrapRef<typeof import('../gql')['GqlGetDownloads']>
    readonly GqlGetOrder: UnwrapRef<typeof import('../gql')['GqlGetOrder']>
    readonly GqlGetOrders: UnwrapRef<typeof import('../gql')['GqlGetOrders']>
    readonly GqlGetProduct: UnwrapRef<typeof import('../gql')['GqlGetProduct']>
    readonly GqlGetProductCategories: UnwrapRef<typeof import('../gql')['GqlGetProductCategories']>
    readonly GqlGetProducts: UnwrapRef<typeof import('../gql')['GqlGetProducts']>
    readonly GqlGetStates: UnwrapRef<typeof import('../gql')['GqlGetStates']>
    readonly GqlGetStockStatus: UnwrapRef<typeof import('../gql')['GqlGetStockStatus']>
    readonly GqlGetStripePaymentIntent: UnwrapRef<typeof import('../gql')['GqlGetStripePaymentIntent']>
    readonly GqlLogin: UnwrapRef<typeof import('../gql')['GqlLogin']>
    readonly GqlLoginWithProvider: UnwrapRef<typeof import('../gql')['GqlLoginWithProvider']>
    readonly GqlLogoutPlaceholder: UnwrapRef<typeof import('../gql')['GqlLogoutPlaceholder']>
    readonly GqlRegisterCustomer: UnwrapRef<typeof import('../gql')['GqlRegisterCustomer']>
    readonly GqlRemoveCoupons: UnwrapRef<typeof import('../gql')['GqlRemoveCoupons']>
    readonly GqlResetPasswordEmail: UnwrapRef<typeof import('../gql')['GqlResetPasswordEmail']>
    readonly GqlResetPasswordKey: UnwrapRef<typeof import('../gql')['GqlResetPasswordKey']>
    readonly GqlUpDateCartQuantity: UnwrapRef<typeof import('../gql')['GqlUpDateCartQuantity']>
    readonly GqlUpdateCustomer: UnwrapRef<typeof import('../gql')['GqlUpdateCustomer']>
    readonly GqlUpdatePassword: UnwrapRef<typeof import('../gql')['GqlUpdatePassword']>
    readonly GqlWriteReview: UnwrapRef<typeof import('../gql')['GqlWriteReview']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly callOnce: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly computed: UnwrapRef<typeof import('../../node_modules/vue')['computed']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly customRef: UnwrapRef<typeof import('../../node_modules/vue')['customRef']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineComponent']>
    readonly defineI18nConfig: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nConfig']>
    readonly defineI18nLocale: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nLocale']>
    readonly defineI18nRoute: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nRoute']>
    readonly defineLazyHydrationComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/lazy-hydration')['defineLazyHydrationComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly effect: UnwrapRef<typeof import('../../node_modules/vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('../../node_modules/vue')['effectScope']>
    readonly getAppManifest: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getCurrentInstance: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentScope']>
    readonly getRouteRules: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly h: UnwrapRef<typeof import('../../node_modules/vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('../../node_modules/vue')['hasInjectionContext']>
    readonly inject: UnwrapRef<typeof import('../../node_modules/vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('../../node_modules/vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('../../node_modules/vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('../../node_modules/vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('../../node_modules/vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('../../node_modules/vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly markRaw: UnwrapRef<typeof import('../../node_modules/vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('../../node_modules/vue')['mergeModels']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('../../node_modules/vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('../../node_modules/vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('../../node_modules/vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('../../node_modules/vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('../../node_modules/vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('../../node_modules/vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('../../node_modules/vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('../../node_modules/vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('../../node_modules/vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('../../node_modules/vue')['onWatcherCleanup']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('../../node_modules/vue')['provide']>
    readonly proxyRefs: UnwrapRef<typeof import('../../node_modules/vue')['proxyRefs']>
    readonly reactive: UnwrapRef<typeof import('../../node_modules/vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('../../node_modules/vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('../../node_modules/vue')['ref']>
    readonly refreshCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('../../node_modules/vue')['resolveComponent']>
    readonly setInterval: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('../../node_modules/vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('../../node_modules/vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('../../node_modules/vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly toRaw: UnwrapRef<typeof import('../../node_modules/vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('../../node_modules/vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('../../node_modules/vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('../../node_modules/vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('../../node_modules/vue')['triggerRef']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('../../node_modules/vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAsyncGql: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useAsyncGql']>
    readonly useAttrs: UnwrapRef<typeof import('../../node_modules/vue')['useAttrs']>
    readonly useAuth: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useAuth')['useAuth']>
    readonly useBrowserLocale: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useBrowserLocale']>
    readonly useCart: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useCart')['useCart']>
    readonly useCheckout: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useCheckout')['useCheckout']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCookieLocale: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useCookieLocale']>
    readonly useCountry: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useCountry')['useCountry']>
    readonly useCssModule: UnwrapRef<typeof import('../../node_modules/vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('../../node_modules/vue')['useCssVars']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFiltering: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useFiltering')['useFiltering']>
    readonly useGql: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGql']>
    readonly useGqlCors: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlCors']>
    readonly useGqlError: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlError']>
    readonly useGqlHeaders: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlHeaders']>
    readonly useGqlHost: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlHost']>
    readonly useGqlToken: UnwrapRef<typeof import('../../node_modules/nuxt-graphql-client/dist/runtime/composables/index')['useGqlToken']>
    readonly useHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']>
    readonly useHelpers: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useHelpers')['useHelpers']>
    readonly useHydration: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useI18n: UnwrapRef<typeof import('../../node_modules/vue-i18n/dist/vue-i18n')['useI18n']>
    readonly useId: UnwrapRef<typeof import('../../node_modules/vue')['useId']>
    readonly useImage: UnwrapRef<typeof import('../../node_modules/@nuxt/image/dist/runtime/composables')['useImage']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('../../node_modules/vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useLocaleHead: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleHead']>
    readonly useLocalePath: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocalePath']>
    readonly useLocaleRoute: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleRoute']>
    readonly useModel: UnwrapRef<typeof import('../../node_modules/vue')['useModel']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useNuxtDevTools: UnwrapRef<typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly useProducts: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useProducts')['useProducts']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouteBaseName: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useRouteBaseName']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useScript: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptRybbitAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptSnapchatPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptUmamiAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useSearching: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useSearch')['useSearching']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']>
    readonly useSetI18nParams: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSetI18nParams']>
    readonly useShadowRoot: UnwrapRef<typeof import('../../node_modules/vue')['useShadowRoot']>
    readonly useSlots: UnwrapRef<typeof import('../../node_modules/vue')['useSlots']>
    readonly useSorting: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useSorting')['useSorting']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useSwitchLocalePath: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSwitchLocalePath']>
    readonly useTemplateRef: UnwrapRef<typeof import('../../node_modules/vue')['useTemplateRef']>
    readonly useTransitionState: UnwrapRef<typeof import('../../node_modules/vue')['useTransitionState']>
    readonly useWishlist: UnwrapRef<typeof import('../../woonuxt_base/app/composables/useWishlist')['useWishlist']>
    readonly watch: UnwrapRef<typeof import('../../node_modules/vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchSyncEffect']>
    readonly withCtx: UnwrapRef<typeof import('../../node_modules/vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('../../node_modules/vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('../../node_modules/vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('../../node_modules/vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('../../node_modules/vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('../../node_modules/vue')['withScopeId']>
  }
}