import e from"node:process";globalThis._importMeta_={url:import.meta.url,env:e.env};import{tmpdir as t}from"node:os";import{defineEventHandler as o,handleCacheHeaders as n,splitCookiesString as r,createEvent as s,fetchWithEvent as a,isEvent as i,event<PERSON><PERSON><PERSON> as c,setHeaders as l,sendRedirect as d,proxyRequest as u,getRequestHeader as p,setResponseHeaders as f,setResponseStatus as h,send as m,getRequestHeaders as g,setResponseHeader as y,appendResponseHeader as b,getRequestURL as w,getResponseHeader as x,removeResponseHeader as v,createError as _,getResponseStatus as A,getQuery as C,readBody as R,lazyEventHandler as k,useBase as E,createApp as j,createRouter as S,toNodeListener as T,getRouterParam as U,getResponseStatusText as O}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/h3/dist/index.mjs";import{Server as L}from"node:http";import{resolve as N,dirname as $,join as P}from"node:path";import D from"node:crypto";import{parentPort as H,threadId as I}from"node:worker_threads";import{escapeHtml as V}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/@vue/shared/dist/shared.cjs.js";import{createRenderer as z,getRequestDependencies as M,getPreloadLinks as F,getPrefetchLinks as B}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/vue-bundle-renderer/dist/runtime.mjs";import W,{destr as q}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/destr/dist/index.mjs";import{createHooks as Q}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/hookable/dist/index.mjs";import{createFetch as K,Headers as X}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/ofetch/dist/node.mjs";import{fetchNodeRequestHandler as G,callNodeRequestHandler as J}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/node-mock-http/dist/index.mjs";import{createStorage as Y,prefixStorage as Z}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/unstorage/dist/index.mjs";import ee from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/unstorage/drivers/fs.mjs";import{digest as te,hash as oe}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/ohash/dist/index.mjs";import{klona as ne}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/klona/dist/index.mjs";import re,{defuFn as se}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/defu/dist/defu.mjs";import{snakeCase as ae}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/scule/dist/index.mjs";import{getContext as ie}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/unctx/dist/index.mjs";import{toRouteMatcher as ce,createRouter as le}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/radix3/dist/index.mjs";import{readFile as de}from"node:fs/promises";import ue,{consola as pe}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/consola/dist/index.mjs";import{ErrorParser as fe}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/youch-core/build/index.js";import{Youch as he}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/nitropack/node_modules/youch/build/index.js";import{SourceMapConsumer as me}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/nitropack/node_modules/source-map/source-map.js";import{AsyncLocalStorage as ge}from"node:async_hooks";import{stringify as ye,uneval as be}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/devalue/index.js";import{captureRawStackTrace as we,parseRawStackTrace as xe}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/errx/dist/index.js";import{isVNode as ve,toValue as _e,isRef as Ae}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/vue/index.mjs";import{GraphQLClient as Ce}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/graphql-request/build/entrypoints/main.js";import{promises as Re}from"node:fs";import{fileURLToPath as ke}from"node:url";import{dirname as Ee,resolve as je,basename as Se,isAbsolute as Te}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/pathe/dist/index.mjs";import{getIcons as Ue}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/@iconify/utils/lib/index.mjs";import{collections as Oe}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/.nuxt/nuxt-icon-server-bundle.mjs";import{createHead as Le,propsToString as Ne,renderSSRHead as $e}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/unhead/dist/server.mjs";import{renderToString as Pe}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/vue/server-renderer/index.mjs";import{walkResolver as De}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/unhead/dist/utils.mjs";import{ipxFSStorage as He,ipxHttpStorage as Ie,createIPX as Ve,createIPXH3Handler as ze}from"file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/node_modules/ipx/dist/index.mjs";const Me=/#/g,Fe=/&/g,Be=/\//g,We=/=/g,qe=/\+/g,Qe=/%5e/gi,Ke=/%60/gi,Xe=/%7c/gi,Ge=/%20/gi,Je=/%2f/gi;function encodeQueryValue(e){return(t="string"==typeof e?e:JSON.stringify(e),encodeURI(""+t).replace(Xe,"|")).replace(qe,"%2B").replace(Ge,"+").replace(Me,"%23").replace(Fe,"%26").replace(Ke,"`").replace(Qe,"^").replace(Be,"%2F");var t}function encodeQueryKey(e){return encodeQueryValue(e).replace(We,"%3D")}function decode(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function decodeQueryKey(e){return decode(e.replace(qe," "))}function decodeQueryValue(e){return decode(e.replace(qe," "))}function parseQuery(e=""){const t=Object.create(null);"?"===e[0]&&(e=e.slice(1));for(const o of e.split("&")){const e=o.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const n=decodeQueryKey(e[1]);if("__proto__"===n||"constructor"===n)continue;const r=decodeQueryValue(e[2]||"");void 0===t[n]?t[n]=r:Array.isArray(t[n])?t[n].push(r):t[n]=[t[n],r]}return t}function stringifyQuery(e){return Object.keys(e).filter(t=>void 0!==e[t]).map(t=>{return o=t,"number"!=typeof(n=e[t])&&"boolean"!=typeof n||(n=String(n)),n?Array.isArray(n)?n.map(e=>`${encodeQueryKey(o)}=${encodeQueryValue(e)}`).join("&"):`${encodeQueryKey(o)}=${encodeQueryValue(n)}`:encodeQueryKey(o);var o,n}).filter(Boolean).join("&")}const Ye=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,Ze=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,et=/^([/\\]\s*){2,}[^/\\]/,tt=/^\.?\//;function hasProtocol(e,t={}){return"boolean"==typeof t&&(t={acceptRelative:t}),t.strict?Ye.test(e):Ze.test(e)||!!t.acceptRelative&&et.test(e)}function withoutTrailingSlash(e="",t){return(function(e=""){return e.endsWith("/")}(e)?e.slice(0,-1):e)||"/"}function withTrailingSlash(e="",t){return e.endsWith("/")?e:e+"/"}function withoutBase(e,t){if(!(o=t)||"/"===o)return e;var o;const n=withoutTrailingSlash(t);if(!e.startsWith(n))return e;const r=e.slice(n.length);return"/"===r[0]?r:"/"+r}function withQuery(e,t){const o=parseURL(e),n={...parseQuery(o.search),...t};return o.search=stringifyQuery(n),function(e){const t=e.pathname||"",o=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",n=e.hash||"",r=e.auth?e.auth+"@":"",s=e.host||"",a=e.protocol||e[ot]?(e.protocol||"")+"//":"";return a+r+s+t+o+n}(o)}function getQuery(e){return parseQuery(parseURL(e).search)}function joinURL(e,...t){let o=e||"";for(const e of t.filter(e=>function(e){return e&&"/"!==e}(e)))if(o){const t=e.replace(tt,"");o=withTrailingSlash(o)+t}else o=e;return o}function joinRelativeURL(...e){const t=/\/(?!\/)/,o=e.filter(Boolean),n=[];let r=0;for(const e of o)if(e&&"/"!==e)for(const[o,s]of e.split(t).entries())if(s&&"."!==s)if(".."!==s)1===o&&n[n.length-1]?.endsWith(":/")?n[n.length-1]+="/"+s:(n.push(s),r++);else{if(1===n.length&&hasProtocol(n[0]))continue;n.pop(),r--}let s=n.join("/");return r>=0?o[0]?.startsWith("/")&&!s.startsWith("/")?s="/"+s:o[0]?.startsWith("./")&&!s.startsWith("./")&&(s="./"+s):s="../".repeat(-1*r)+s,o[o.length-1]?.endsWith("/")&&!s.endsWith("/")&&(s+="/"),s}const ot=Symbol.for("ufo:protocolRelative");function parseURL(e="",t){const o=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(o){const[,e,t=""]=o;return{protocol:e.toLowerCase(),pathname:t,href:e+t,auth:"",host:"",search:"",hash:""}}if(!hasProtocol(e,{acceptRelative:!0}))return t?parseURL(t+e):parsePath(e);const[,n="",r,s=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,a="",i=""]=s.match(/([^#/?]*)(.*)?/)||[];"file:"===n&&(i=i.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:c,search:l,hash:d}=parsePath(i);return{protocol:n.toLowerCase(),auth:r?r.slice(0,Math.max(0,r.length-1)):"",host:a,pathname:c,search:l,hash:d,[ot]:!n}}function parsePath(e=""){const[t="",o="",n=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:o,hash:n}}const nt=[{baseName:"server",dir:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/server/assets"}],rt=Y();for(const e of nt)rt.mount(e.baseName,ee({base:e.dir,ignore:e?.ignore||[]}));const st=Y({});function useStorage(e=""){return e?Z(st,e):st}st.mount("/assets",rt),st.mount("root",ee({driver:"fs",readOnly:!0,base:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend",watchOptions:{ignored:[null]}})),st.mount("src",ee({driver:"fs",readOnly:!0,base:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/server",watchOptions:{ignored:[null]}})),st.mount("build",ee({driver:"fs",readOnly:!1,base:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/.nuxt"})),st.mount("cache",ee({driver:"fs",readOnly:!1,base:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/.nuxt/cache"})),st.mount("data",ee({driver:"fs",base:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/.data/kv"}));const at=(()=>{class Hasher2{buff="";#e=new Map;write(e){this.buff+=e}dispatch(e){return this[null===e?"null":typeof e](e)}object(e){if(e&&"function"==typeof e.toJSON)return this.object(e.toJSON());const t=Object.prototype.toString.call(e);let o="";const n=t.length;o=n<10?"unknown:["+t+"]":t.slice(8,n-1),o=o.toLowerCase();let r=null;if(void 0!==(r=this.#e.get(e)))return this.dispatch("[CIRCULAR:"+r+"]");if(this.#e.set(e,this.#e.size),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(e))return this.write("buffer:"),this.write(e.toString("utf8"));if("object"!==o&&"function"!==o&&"asyncfunction"!==o)this[o]?this[o](e):this.unknown(e,o);else{const t=Object.keys(e).sort(),o=[];this.write("object:"+(t.length+o.length)+":");const dispatchForKey=t=>{this.dispatch(t),this.write(":"),this.dispatch(e[t]),this.write(",")};for(const e of t)dispatchForKey(e);for(const e of o)dispatchForKey(e)}}array(e,t){if(t=void 0!==t&&t,this.write("array:"+e.length+":"),!t||e.length<=1){for(const t of e)this.dispatch(t);return}const o=new Map,n=e.map(e=>{const t=new Hasher2;t.dispatch(e);for(const[e,n]of t.#e)o.set(e,n);return t.toString()});return this.#e=o,n.sort(),this.array(n,!1)}date(e){return this.write("date:"+e.toJSON())}symbol(e){return this.write("symbol:"+e.toString())}unknown(e,t){if(this.write(t),e)return this.write(":"),e&&"function"==typeof e.entries?this.array([...e.entries()],!0):void 0}error(e){return this.write("error:"+e.toString())}boolean(e){return this.write("bool:"+e)}string(e){this.write("string:"+e.length+":"),this.write(e)}function(e){this.write("fn:"),!function(e){if("function"!=typeof e)return!1;return"[native code] }"===Function.prototype.toString.call(e).slice(-15)}(e)?this.dispatch(e.toString()):this.dispatch("[native]")}number(e){return this.write("number:"+e)}null(){return this.write("Null")}undefined(){return this.write("Undefined")}regexp(e){return this.write("regex:"+e.toString())}arraybuffer(e){return this.write("arraybuffer:"),this.dispatch(new Uint8Array(e))}url(e){return this.write("url:"+e.toString())}map(e){this.write("map:");const t=[...e];return this.array(t,!1)}set(e){this.write("set:");const t=[...e];return this.array(t,!1)}bigint(e){return this.write("bigint:"+e.toString())}}for(const e of["uint8array","uint8clampedarray","unt8array","uint16array","unt16array","uint32array","unt32array","float32array","float64array"])Hasher2.prototype[e]=function(t){return this.write(e+":"),this.array([...t],!1)};return Hasher2})();function hash(e){return te("string"==typeof e?e:function(e){const t=new at;return t.dispatch(e),t.buff}(e)).replace(/[-_]/g,"").slice(0,10)}function defineCachedFunction(e,t={}){t={name:"_",base:"/cache",swr:!0,maxAge:1,...t};const o={},n=t.group||"nitro/functions",r=t.name||e.name||"_",s=t.integrity||hash([e,t]),a=t.validate||(e=>void 0!==e.value);return async(...c)=>{if(await(t.shouldBypassCache?.(...c)))return e(...c);const l=await(t.getKey||getKey)(...c),d=await(t.shouldInvalidateCache?.(...c)),u=await async function(e,i,c,l){const d=[t.base,n,r,e+".json"].filter(Boolean).join(":").replace(/:\/$/,":index");let u=await useStorage().getItem(d).catch(e=>{console.error("[cache] Cache read error.",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})})||{};if("object"!=typeof u){u={};const e=new Error("Malformed data read from cache.");console.error("[cache]",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})}const p=1e3*(t.maxAge??0);p&&(u.expires=Date.now()+p);const f=c||u.integrity!==s||p&&Date.now()-(u.mtime||0)>p||!1===a(u),h=f?(async()=>{const n=o[e];n||(void 0!==u.value&&(t.staleMaxAge||0)>=0&&!1===t.swr&&(u.value=void 0,u.integrity=void 0,u.mtime=void 0,u.expires=void 0),o[e]=Promise.resolve(i()));try{u.value=await o[e]}catch(t){throw n||delete o[e],t}if(!n&&(u.mtime=Date.now(),u.integrity=s,delete o[e],!1!==a(u))){let e;t.maxAge&&!t.swr&&(e={ttl:t.maxAge});const o=useStorage().setItem(d,u,e).catch(e=>{console.error("[cache] Cache write error.",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})});l?.waitUntil&&l.waitUntil(o)}})():Promise.resolve();return void 0===u.value?await h:f&&l&&l.waitUntil&&l.waitUntil(h),t.swr&&!1!==a(u)?(h.catch(e=>{console.error("[cache] SWR handler error.",e),useNitroApp().captureError(e,{event:l,tags:["cache"]})}),u):h.then(()=>u)}(l,()=>e(...c),d,c[0]&&i(c[0])?c[0]:void 0);let p=u.value;return t.transform&&(p=await t.transform(u,...c)||p),p}}function getKey(...e){return e.length>0?hash(e):""}function escapeKey(e){return String(e).replace(/\W/g,"")}function defineCachedEventHandler(e,t={name:"_",base:"/cache",swr:!0,maxAge:1}){const i=(t.varies||[]).filter(Boolean).map(e=>e.toLowerCase()).sort(),c={...t,getKey:async e=>{const o=await(t.getKey?.(e));if(o)return escapeKey(o);const n=e.node.req.originalUrl||e.node.req.url||e.path;let r;try{r=escapeKey(decodeURI(parseURL(n).pathname)).slice(0,16)||"index"}catch{r="-"}return[`${r}.${hash(n)}`,...i.map(t=>[t,e.node.req.headers[t]]).map(([e,t])=>`${escapeKey(e)}.${hash(t)}`)].join(":")},validate:e=>!!e.value&&(!(e.value.code>=400)&&(void 0!==e.value.body&&("undefined"!==e.value.headers.etag&&"undefined"!==e.value.headers["last-modified"]))),group:t.group||"nitro/handlers",integrity:t.integrity||hash([e,t])},l=function(e,t={}){return defineCachedFunction(e,t)}(async o=>{const n={};for(const e of i){const t=o.node.req.headers[e];void 0!==t&&(n[e]=t)}const r=cloneWithProxy(o.node.req,{headers:n}),l={};let d;const u=cloneWithProxy(o.node.res,{statusCode:200,writableEnded:!1,writableFinished:!1,headersSent:!1,closed:!1,getHeader:e=>l[e],setHeader(e,t){return l[e]=t,this},getHeaderNames:()=>Object.keys(l),hasHeader:e=>e in l,removeHeader(e){delete l[e]},getHeaders:()=>l,end(e,t,o){return"string"==typeof e&&(d=e),"function"==typeof t&&t(),"function"==typeof o&&o(),this},write:(e,t,o)=>("string"==typeof e&&(d=e),"function"==typeof t&&t(void 0),"function"==typeof o&&o(),!0),writeHead(e,t){if(this.statusCode=e,t){if(Array.isArray(t)||"string"==typeof t)throw new TypeError("Raw headers  is not supported.");for(const e in t){const o=t[e];void 0!==o&&this.setHeader(e,o)}}return this}}),p=s(r,u);p.fetch=(e,t)=>a(p,e,t,{fetch:useNitroApp().localFetch}),p.$fetch=(e,t)=>a(p,e,t,{fetch:globalThis.$fetch}),p.waitUntil=o.waitUntil,p.context=o.context,p.context.cache={options:c};const f=await e(p)||d,h=p.node.res.getHeaders();h.etag=String(h.Etag||h.etag||`W/"${hash(f)}"`),h["last-modified"]=String(h["Last-Modified"]||h["last-modified"]||(new Date).toUTCString());const m=[];t.swr?(t.maxAge&&m.push(`s-maxage=${t.maxAge}`),t.staleMaxAge?m.push(`stale-while-revalidate=${t.staleMaxAge}`):m.push("stale-while-revalidate")):t.maxAge&&m.push(`max-age=${t.maxAge}`),m.length>0&&(h["cache-control"]=m.join(", "));return{code:p.node.res.statusCode,headers:h,body:f}},c);return o(async o=>{if(t.headersOnly){if(n(o,{maxAge:t.maxAge}))return;return e(o)}const s=await l(o);if(o.node.res.headersSent||o.node.res.writableEnded)return s.body;if(!n(o,{modifiedTime:new Date(s.headers["last-modified"]),etag:s.headers.etag,maxAge:t.maxAge})){o.node.res.statusCode=s.code;for(const e in s.headers){const t=s.headers[e];"set-cookie"===e?o.node.res.appendHeader(e,r(t)):void 0!==t&&o.node.res.setHeader(e,t)}return s.body}})}function cloneWithProxy(e,t){return new Proxy(e,{get:(e,o,n)=>o in t?t[o]:Reflect.get(e,o,n),set:(e,o,n,r)=>o in t?(t[o]=n,!0):Reflect.set(e,o,n,r)})}const it=defineCachedEventHandler,ct=se({siteName:"WooNuxt",shortDescription:"This is an example of a WooNuxt store. It provides a modern, fast, and SEO friendly ecommerce store built with Nuxt and WooCommerce.",description:"WooNuxt is unmatched when it comes to performance and scalability. Reap the benefits of having a online store that out performs all of your competitors. You can edit components to display your own information just like the one you're reading now.",baseUrl:"https://v3.woonuxt.com",siteImage:"https://user-images.githubusercontent.com/5116925/218879668-f4c1f9fd-bef4-44b0-bc7f-e87d994aa3a1.png",storeSettings:{autoOpenCart:!1,showReviews:!0,showFilters:!0,showOrderByDropdown:!0,showSKU:!0,showRelatedProducts:!0,showProductCategoriesOnSingleProduct:!0,showBreadcrumbOnSingleProduct:!0,showMoveToWishlist:!0,hideBillingAddressForVirtualProducts:!1,initStoreOnUserActionToReduceServerLoad:!0,saleBadge:"percent",socialLoginsDisplay:"buttons"}},{nuxt:{},icon:{provider:"server",class:"",aliases:{},iconifyApiEndpoint:"https://api.iconify.design",localApiEndpoint:"/api/_nuxt_icon",fallbackToApi:!0,cssSelectorPrefix:"i-",cssWherePseudo:!0,mode:"css",attrs:{"aria-hidden":!0},collections:["academicons","akar-icons","ant-design","arcticons","basil","bi","bitcoin-icons","bpmn","brandico","bx","bxl","bxs","bytesize","carbon","catppuccin","cbi","charm","ci","cib","cif","cil","circle-flags","circum","clarity","codicon","covid","cryptocurrency","cryptocurrency-color","dashicons","devicon","devicon-plain","ei","el","emojione","emojione-monotone","emojione-v1","entypo","entypo-social","eos-icons","ep","et","eva","f7","fa","fa-brands","fa-regular","fa-solid","fa6-brands","fa6-regular","fa6-solid","fad","fe","feather","file-icons","flag","flagpack","flat-color-icons","flat-ui","flowbite","fluent","fluent-emoji","fluent-emoji-flat","fluent-emoji-high-contrast","fluent-mdl2","fontelico","fontisto","formkit","foundation","fxemoji","gala","game-icons","geo","gg","gis","gravity-ui","gridicons","grommet-icons","guidance","healthicons","heroicons","heroicons-outline","heroicons-solid","hugeicons","humbleicons","ic","icomoon-free","icon-park","icon-park-outline","icon-park-solid","icon-park-twotone","iconamoon","iconoir","icons8","il","ion","iwwa","jam","la","lets-icons","line-md","logos","ls","lucide","lucide-lab","mage","majesticons","maki","map","marketeq","material-symbols","material-symbols-light","mdi","mdi-light","medical-icon","memory","meteocons","mi","mingcute","mono-icons","mynaui","nimbus","nonicons","noto","noto-v1","octicon","oi","ooui","openmoji","oui","pajamas","pepicons","pepicons-pencil","pepicons-pop","pepicons-print","ph","pixelarticons","prime","ps","quill","radix-icons","raphael","ri","rivet-icons","si-glyph","simple-icons","simple-line-icons","skill-icons","solar","streamline","streamline-emojis","subway","svg-spinners","system-uicons","tabler","tdesign","teenyicons","token","token-branded","topcoat","twemoji","typcn","uil","uim","uis","uit","uiw","unjs","vaadin","vs","vscode-icons","websymbol","weui","whh","wi","wpf","zmdi","zondicons"],fetchTimeout:1500}});function getEnv(t,o){const n=ae(t).toUpperCase();return W(e.env[o.prefix+n]??e.env[o.altPrefix+n])}function _isObject(e){return"object"==typeof e&&!Array.isArray(e)}function applyEnv(e,t,o=""){for(const n in e){const r=o?`${o}_${n}`:n,s=getEnv(r,t);_isObject(e[n])?_isObject(s)?(e[n]={...e[n],...s},applyEnv(e[n],t,r)):void 0===s?applyEnv(e[n],t,r):e[n]=s??e[n]:e[n]=s??e[n],t.envExpansion&&"string"==typeof e[n]&&(e[n]=_expandFromEnv(e[n]))}return e}const lt=/\{\{([^{}]*)\}\}/g;function _expandFromEnv(t){return t.replace(lt,(t,o)=>e.env[o]||t)}const dt={app:{baseURL:"/",buildId:"dev",buildAssetsDir:"/_nuxt/",cdnURL:""},nitro:{envPrefix:"NUXT_",routeRules:{"/__nuxt_error":{cache:!1},"/checkout/order-received/**":{ssr:!1},"/order-summary/**":{ssr:!1},"/_nuxt/builds/meta/**":{headers:{"cache-control":"public, max-age=31536000, immutable"}},"/_nuxt/builds/**":{headers:{"cache-control":"public, max-age=1, immutable"}}}},public:{LOGO:"/wp-content/uploads/2025/08/cropped-logo.jpg",PRODUCTS_PER_PAGE:24,GLOBAL_PRODUCT_ATTRIBUTES:[],MAX_PRICE:30,FRONT_END_URL:"http://localhost:3000",BACKEND_URL:"http://localhost:8080",CURRENCY_CODE:"",CURRENCY_SYMBOL:"",WOO_NUXT_SEO:"","graphql-client":{clients:{default:{token:{type:"Bearer",name:"Authorization"},proxyCookies:!0,tokenStorage:{mode:"cookie",cookieOptions:{maxAge:604800,secure:!1},name:"gql:default"},preferGETQueries:!1,host:"http://localhost:8080/graphql",corsOptions:{mode:"cors",credentials:"include"},headers:{Origin:"http://localhost:3000"}}}},i18n:{baseUrl:"",defaultLocale:"en_US",defaultDirection:"ltr",strategy:"no_prefix",lazy:!1,rootRedirect:"",routesNameSeparator:"___",defaultLocaleRouteNameSuffix:"default",skipSettingLocaleOnNavigate:!1,differentDomains:!1,trailingSlash:!1,locales:[{code:"en_US",name:"English 🇺🇸",files:[{path:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/locales/en-US.json",cache:""}]},{code:"de_DE",name:"Deutsch 🇩🇪",files:[{path:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/locales/de-DE.json",cache:""}]},{code:"es_ES",name:"Español 🇪🇸",files:[{path:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/locales/es-ES.json",cache:""}]},{code:"fr_FR",name:"Français 🇫🇷",files:[{path:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/locales/fr-FR.json",cache:""}]},{code:"it_IT",name:"Italiano 🇮🇹",files:[{path:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/locales/it-IT.json",cache:""}]},{code:"pt_BR",name:"Português 🇧🇷",files:[{path:"/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/locales/pt-BR.json",cache:""}]}],detectBrowserLanguage:{alwaysRedirect:!1,cookieCrossOrigin:!1,cookieDomain:"",cookieKey:"i18n_redirected",cookieSecure:!1,fallbackLocale:"",redirectOn:"root",useCookie:!0},experimental:{localeDetector:"",switchLocalePathLinkSSR:!1,autoImportTranslationFunctions:!1,typedPages:!0,typedOptionsAndMessages:!1,generatedLocaleFilePathFormat:"absolute",alternateLinkCanonicalQueries:!1,hmr:!0},multiDomainLocales:!1,domainLocales:{en_US:{domain:""},de_DE:{domain:""},es_ES:{domain:""},fr_FR:{domain:""},it_IT:{domain:""},pt_BR:{domain:""}}}},"graphql-client":{clients:{}},icon:{serverKnownCssClasses:[]},ipx:{baseURL:"/_ipx",alias:{},fs:{dir:["/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/public"]},http:{domains:[]}}},ut={prefix:"NITRO_",altPrefix:dt.nitro.envPrefix??e.env.NITRO_ENV_PREFIX??"_",envExpansion:dt.nitro.envExpansion??e.env.NITRO_ENV_EXPANSION??!1},pt=_deepFreeze(applyEnv(ne(dt),ut));function useRuntimeConfig(e){if(!e)return pt;if(e.context.nitro.runtimeConfig)return e.context.nitro.runtimeConfig;const t=ne(dt);return applyEnv(t,ut),e.context.nitro.runtimeConfig=t,t}const ft=_deepFreeze(ne(ct));function _deepFreeze(e){const t=Object.getOwnPropertyNames(e);for(const o of t){const t=e[o];t&&"object"==typeof t&&_deepFreeze(t)}return Object.freeze(e)}new Proxy(Object.create(null),{get:(e,t)=>{console.warn("Please use `useRuntimeConfig()` instead of accessing config directly.");const o=useRuntimeConfig();if(t in o)return o[t]}}),ie("nitro-app",{asyncContext:!1,AsyncLocalStorage:void 0});const ht=ce(le({routes:useRuntimeConfig().nitro.routeRules}));function getRouteRules(e){return e.context._nitro=e.context._nitro||{},e.context._nitro.routeRules||(e.context._nitro.routeRules=getRouteRulesForPath(withoutBase(e.path.split("?")[0],useRuntimeConfig().app.baseURL))),e.context._nitro.routeRules}function getRouteRulesForPath(e){return re({},...ht.matchAll(e).reverse())}function _captureError(e,t){console.error(`[${t}]`,e),useNitroApp().captureError(e,{tags:[t]})}function joinHeaders(e){return Array.isArray(e)?e.join(", "):String(e)}function normalizeCookieHeader(e=""){return r(joinHeaders(e))}function normalizeCookieHeaders(e){const t=new Headers;for(const[o,n]of e)if("set-cookie"===o)for(const e of normalizeCookieHeader(n))t.append("set-cookie",e);else t.set(o,joinHeaders(n));return t}function hasReqHeader(e,t,o){const n=p(e,t);return n&&"string"==typeof n&&n.toLowerCase().includes(o)}async function defaultHandler(t,o,n){const r=t.unhandled||t.fatal,s=t.statusCode||500,a=t.statusMessage||"Server Error",i=w(o,{xForwardedHost:!0,xForwardedProto:!0});if(404===s){const e="/";if(/^\/[^/]/.test(e)&&!i.pathname.startsWith(e)){return{status:302,statusText:"Found",headers:{location:`${e}${i.pathname.slice(1)}${i.search}`},body:"Redirecting..."}}}await loadStackTrace(t).catch(ue.error);const c=new he;if(r&&!n?.silent){const n=[t.unhandled&&"[unhandled]",t.fatal&&"[fatal]"].filter(Boolean).join(" "),r=await(await c.toANSI(t)).replaceAll(e.cwd(),".");ue.error(`[request error] ${n} [${o.method}] ${i}\n\n`,r)}const l=n?.json||!p(o,"accept")?.includes("text/html"),d={"content-type":l?"application/json":"text/html","x-content-type-options":"nosniff","x-frame-options":"DENY","referrer-policy":"no-referrer","content-security-policy":"script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"};404!==s&&x(o,"cache-control")||(d["cache-control"]="no-cache");return{status:s,statusText:a,headers:d,body:l?{error:!0,url:i,statusCode:s,statusMessage:a,message:t.message,data:t.data,stack:t.stack?.split("\n").map(e=>e.trim())}:await c.toHTML(t,{request:{url:i.href,method:o.method,headers:g(o)}})}}async function loadStackTrace(e){if(!(e instanceof Error))return;const t=await(new fe).defineSourceLoader(sourceLoader).parse(e),o=e.message+"\n"+t.frames.map(e=>function(e){if("native"===e.type)return e.raw;const t=`${e.fileName||""}:${e.lineNumber}:${e.columnNumber})`;return e.functionName?`at ${e.functionName} (${t}`:`at ${t}`}(e)).join("\n");Object.defineProperty(e,"stack",{value:o}),e.cause&&await loadStackTrace(e.cause).catch(ue.error)}async function sourceLoader(e){if(!e.fileName||"fs"!==e.fileType||"native"===e.type)return;if("app"===e.type){const t=await de(`${e.fileName}.map`,"utf8").catch(()=>{});if(t){const o=(await new me(t)).originalPositionFor({line:e.lineNumber,column:e.columnNumber});o.source&&o.line&&(e.fileName=N($(e.fileName),o.source),e.lineNumber=o.line,e.columnNumber=o.column||0)}}const t=await de(e.fileName,"utf8").catch(()=>{});return t?{contents:t}:void 0}const mt=[async function(e,t,{defaultHandler:o}){if(t.handled||function(e){return!hasReqHeader(e,"accept","text/html")&&(hasReqHeader(e,"accept","application/json")||hasReqHeader(e,"user-agent","curl/")||hasReqHeader(e,"user-agent","httpie/")||hasReqHeader(e,"sec-fetch-mode","cors")||e.path.startsWith("/api/")||e.path.endsWith(".json"))}(t))return;const n=await o(e,t,{json:!0});if(404===(e.statusCode||500)&&302===n.status)return f(t,n.headers),h(t,n.status,n.statusText),m(t,JSON.stringify(n.body,null,2));"string"!=typeof n.body&&Array.isArray(n.body.stack)&&(n.body.stack=n.body.stack.join("\n"));const r=n.body,s=new URL(r.url);r.url=withoutBase(s.pathname,useRuntimeConfig(t).app.baseURL)+s.search+s.hash,r.message||="Server Error",r.data||=e.data,r.statusMessage||=e.statusMessage,delete n.headers["content-type"],delete n.headers["content-security-policy"],f(t,n.headers);const a=g(t),i=t.path.startsWith("/__nuxt_error")||!!a["x-nuxt-error"]?null:await useNitroApp().localFetch(withQuery(joinURL(useRuntimeConfig(t).app.baseURL,"/__nuxt_error"),r),{headers:{...a,"x-nuxt-error":"true"},redirect:"manual"}).catch(()=>null);if(t.handled)return;if(!i){const{template:e}=await Promise.resolve().then(function(){return oo});return r.description=r.message,y(t,"Content-Type","text/html;charset=UTF-8"),m(t,e(r))}const c=await i.text();for(const[e,o]of i.headers.entries())"set-cookie"!==e?y(t,e,o):b(t,e,o);return h(t,i.status&&200!==i.status?i.status:n.status,i.statusText||n.statusText),m(t,c)},async function(e,t){const o=await defaultHandler(e,t);return t.node?.res.headersSent||f(t,o.headers),h(t,o.status,o.statusText),m(t,"string"==typeof o.body?o.body:JSON.stringify(o.body,null,2))}];const gt={meta:[{name:"viewport",content:"width=device-width, initial-scale=1"},{charset:"utf-8"}],link:[{rel:"icon",href:"/logo.svg",type:"image/svg+xml"}],style:[],script:[],noscript:[],htmlAttrs:{lang:"en"}},yt="div",bt={id:"teleports"},wt={id:"__nuxt-loader"},xt="nuxt-app",vt={VNode:e=>ve(e)?{type:e.type,props:e.props}:void 0,URL:e=>e instanceof URL?e.toString():void 0},_t=ie("nuxt-dev",{asyncContext:!0,AsyncLocalStorage:ge}),At=/\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;const Ct={clients:{},config:{default:{token:{type:"Bearer",name:"Authorization"},proxyCookies:!0,tokenStorage:{mode:"cookie",cookieOptions:{maxAge:604800,secure:!1},name:"gql:default"},preferGETQueries:!1,host:"http://localhost:8080/graphql",corsOptions:{mode:"cors",credentials:"include"},headers:{Origin:"http://localhost:3000"}}}},Rt=[function(e){e.hooks.hook("render:html",e=>{e.head.push("<script>\nif (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {\n  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {\n    value: {},\n    enumerable: false,\n    configurable: true,\n  })\n}\nwindow.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()\n<\/script>")})},e=>{const t=e.h3App.handler;var o;e.h3App.handler=e=>_t.callAsync({logs:[],event:e},()=>t(e)),o=e=>{const t=_t.tryUse();if(!t)return;const o=we();if(!o||o.includes("runtime/vite-node.mjs"))return;const n=[];let r="";for(const e of xe(o))e.source!==globalThis._importMeta_.url&&(At.test(e.source)||(r||=e.source.replace(withTrailingSlash("/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend"),""),n.push({...e,source:e.source.startsWith("file://")?e.source.replace("file://",""):e.source})));const s={...e,filename:r,stack:n};t.logs.push(s)},pe.addReporter({log(e){o(e)}}),pe.wrapConsole(),e.hooks.hook("afterResponse",()=>{const t=_t.tryUse();if(t)return e.hooks.callHook("dev:ssr-logs",{logs:t.logs,path:t.event.path})}),e.hooks.hook("render:html",e=>{const t=_t.tryUse();if(t)try{const o=Object.assign(Object.create(null),vt,t.event.context._payloadReducers);e.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${xt}">${ye(t.logs,o)}<\/script>`)}catch(e){const t=e instanceof Error&&"toString"in e?` Received \`${e.toString()}\`.`:"";console.warn(`[nuxt] Failed to stringify dev server logs.${t} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`)}})},()=>{const e=Ct.config;for(const[t,o]of Object.entries(e)){const e="object"==typeof o?.headers?.serverOnly&&o?.headers?.serverOnly||void 0;o?.headers?.serverOnly&&delete o.headers.serverOnly;const n=o.token.name,r=o.token.type,s=r?`${r} ${o?.token?.value}`:o?.token?.value,a={...o?.headers,...e,...o?.token?.value&&{[n]:s}};Ct.clients[t]=new Ce(o.host,{headers:a,...o.fetchOptions})}}],kt={};const Et={"/_nuxt/builds/meta/":{maxAge:31536e3},"/_nuxt/builds/":{maxAge:1}};function getAsset(e){return kt[e]}const jt=new Set(["HEAD","GET"]),St={gzip:".gz",br:".br"},Tt=c(e=>{if(e.method&&!jt.has(e.method))return;let t=decode(function(e=""){return function(e=""){return e.startsWith("/")}(e)?e:"/"+e}(withoutTrailingSlash(parseURL(e.path).pathname)).replace(Je,"%252F"));let o;const n=[...String(p(e,"accept-encoding")||"").split(",").map(e=>St[e.trim()]).filter(Boolean).sort(),""];n.length>1&&b(e,"Vary","Accept-Encoding");for(const e of n)for(const n of[t+e,joinURL(t,"index.html"+e)]){const e=getAsset(n);if(e){o=e,t=n;break}}if(!o){if(function(e=""){if(kt[e])return!0;for(const t in Et)if(e.startsWith(t))return!0;return!1}(t))throw v(e,"Cache-Control"),_({statusCode:404});return}if(p(e,"if-none-match")===o.etag)return h(e,304,"Not Modified"),"";const r=p(e,"if-modified-since"),s=new Date(o.mtime);return r&&o.mtime&&new Date(r)>=s?(h(e,304,"Not Modified"),""):(o.type&&!x(e,"Content-Type")&&y(e,"Content-Type",o.type),o.etag&&!x(e,"ETag")&&y(e,"ETag",o.etag),o.mtime&&!x(e,"Last-Modified")&&y(e,"Last-Modified",s.toUTCString()),o.encoding&&!x(e,"Content-Encoding")&&y(e,"Content-Encoding",o.encoding),o.size>0&&!x(e,"Content-Length")&&y(e,"Content-Length",o.size),function(e){const t=Ee(ke(globalThis._importMeta_.url));return Re.readFile(je(t,kt[e].path))}(t))});const Ut={},Ot={};function buildAssetsURL(...e){return joinRelativeURL(publicAssetsURL(),useRuntimeConfig().app.buildAssetsDir,...e)}function publicAssetsURL(...e){const t=useRuntimeConfig().app,o=t.cdnURL||t.baseURL;return e.length?joinRelativeURL(o,...e):o}const Lt=new Set,Nt="https://api.iconify.design",$t=defineCachedEventHandler(async e=>{const t=w(e);if(!t)return _({status:400,message:"Invalid icon request"});const o=ft.icon,n=e.context.params?.collection?.replace(/\.json$/,""),r=n?await(Oe[n]?.()):null,s=o.iconifyApiEndpoint||Nt,a=t.searchParams.get("icons")?.split(",");if(r){if(a?.length){const e=Ue(r,a);return pe.debug(`[Icon] serving ${(a||[]).map(e=>"`"+n+":"+e+"`").join(",")} from bundled collection`),e}}else n&&!Lt.has(n)&&s===Nt&&(pe.warn([`[Icon] Collection \`${n}\` is not found locally`,`We suggest to install it via \`npm i -D @iconify-json/${n}\` to provide the best end-user experience.`].join("\n")),Lt.add(n));if(!0===o.fallbackToApi||"server-only"===o.fallbackToApi){const e=new URL("./"+Se(t.pathname)+t.search,s);if(pe.debug(`[Icon] fetching ${(a||[]).map(e=>"`"+n+":"+e+"`").join(",")} from iconify api`),e.host!==new URL(s).host)return _({status:400,message:"Invalid icon request"});try{return await $fetch(e.href)}catch(e){return pe.error(e),404===e.status?_({status:404}):_({status:500,message:"Failed to fetch fallback icon"})}}return _({status:404})},{group:"nuxt",name:"icon",getKey(e){const t=e.context.params?.collection?.replace(/\.json$/,"")||"unknown",o=String(C(e).icons||"");return`${t}_${o.split(",")[0]}_${o.length}_${oe(o)}`},swr:!0,maxAge:604800}),VueResolver=(e,t)=>Ae(t)?_e(t):t;function resolveUnrefHeadInput(e){return De(e,VueResolver)}function createHead(e={}){const t=Le({...e,propResolvers:[VueResolver]});return t.install=function(e){return{install(t){t.config.globalProperties.$unhead=e,t.config.globalProperties.$head=e,t.provide("usehead",e)}}.install}(t),t}const Pt={disableDefaults:!0};function createSSRContext(e){return{url:e.path,event:e,runtimeConfig:useRuntimeConfig(e),noSSR:e.context.nuxt?.noSSR||!1,head:createHead(Pt),error:!1,nuxt:void 0,payload:{},_payloadReducers:Object.create(null),modules:new Set}}const Dt=`<${yt}${Ne({id:"__nuxt"})}>`,Ht=`</${yt}>`,getClientManifest=()=>import("file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/.nuxt//dist/server/client.manifest.mjs").then(e=>e.default||e).then(e=>"function"==typeof e?e():e),It=lazyCachedFunction(async()=>{const t=await getClientManifest();if(!t)throw new Error("client.manifest is not available");const o=await import("file:///home/<USER>/%E6%A1%8C%E9%9D%A2/pet-ecommerce-store-V14.0/frontend/.nuxt//dist/server/server.mjs").then(e=>e.default||e);if(!o)throw new Error("Server bundle is not available");const n=z(o,{manifest:t,renderToString:async function(t,o){const r=await Pe(t,o);e.env.NUXT_VITE_NODE_OPTIONS&&n.rendererContext.updateManifest(await getClientManifest());return Dt+r+Ht},buildAssetsURL:buildAssetsURL});return n}),Vt=lazyCachedFunction(async()=>{const e=await getClientManifest(),t=await Promise.resolve().then(function(){return no}).then(e=>e.template).catch(()=>"").then(e=>{{const t=`<div${Ne(wt)}>`;return Dt+Ht+(e?t+e+"</div>":"")}}),o=z(()=>()=>{},{manifest:e,renderToString:()=>t,buildAssetsURL:buildAssetsURL}),n=await o.renderToString({});return{rendererContext:o.rendererContext,renderToString:e=>{const t=useRuntimeConfig(e.event);return e.modules||=new Set,e.payload.serverRendered=!1,e.config={public:t.public,app:t.app},Promise.resolve(n)}}});function lazyCachedFunction(e){let t=null;return()=>(null===t&&(t=e().catch(e=>{throw t=null,e})),t)}const zt=lazyCachedFunction(()=>Promise.resolve().then(function(){return ro}).then(e=>e.default||e));const Mt=new RegExp(`^<${yt}[^>]*>([\\s\\S]*)<\\/${yt}>$`);function getServerComponentHTML(e){const t=e.match(Mt);return t?.[1]||e}const Ft=/^uid=([^;]*);slot=(.*)$/,Bt=/^uid=([^;]*);client=(.*)$/,Wt=/^island-slot=([^;]*);(.*)$/;function getSlotIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.slots).length)return;const t={};for(const[o,n]of Object.entries(e.islandContext.slots))t[o]={...n,fallback:e.teleports?.[`island-fallback=${o}`]};return t}function getClientIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.components).length)return;const t={};for(const[o,n]of Object.entries(e.islandContext.components)){const r=e.teleports?.[o]?.replaceAll("\x3c!--teleport start anchor--\x3e","")||"";t[o]={...n,html:r,slots:getComponentSlotTeleport(o,e.teleports??{})}}return t}function getComponentSlotTeleport(e,t){const o=Object.entries(t),n={};for(const[t,r]of o){const o=t.match(Wt);if(o){const[,t,s]=o;if(!s||e!==t)continue;n[s]=r}}return n}function replaceIslandTeleports(e,t){const{teleports:o,islandContext:n}=e;if(n||!o)return t;for(const e in o){const n=e.match(Bt);if(n){const[,r,s]=n;if(!r||!s)continue;t=t.replace(new RegExp(` data-island-uid="${r}" data-island-component="${s}"[^>]*>`),t=>t+o[e]);continue}const r=e.match(Ft);if(r){const[,n,s]=r;if(!n||!s)continue;t=t.replace(new RegExp(` data-island-uid="${n}" data-island-slot="${s}"[^>]*>`),t=>t+o[e])}}return t}const qt=/\.json(\?.*)?$/,Qt=o(async e=>{const t=useNitroApp();f(e,{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"});const o=await async function(e){let t=e.path||"";const o=t.substring(15).replace(qt,"").split("_"),n=o.length>1?o.pop():void 0,r=o.join("_"),s="GET"===e.method?C(e):await R(e);return{url:"/",...s,id:n,name:r,props:q(s.props)||{},slots:{},components:{}}}(e),n={...createSSRContext(e),islandContext:o,noSSR:!1,url:o.url},r=await It(),s=await r.renderToString(n).catch(async e=>{throw await(n.nuxt?.hooks.callHook("app:error",e)),e}),a=await async function(e){const t=await zt(),o=new Set;for(const n of e)if(n in t&&t[n])for(const e of await t[n]())o.add(e);return Array.from(o).map(e=>({innerHTML:e}))}(n.modules??[]);await(n.nuxt?.hooks.callHook("app:rendered",{ssrContext:n,renderResult:s})),a.length&&n.head.push({style:a});{const{styles:e}=M(n,r.rendererContext),t=[];for(const o of Object.values(e))"inline"in getQuery(o.file)||o.file.includes("scoped")&&!o.file.includes("pages/")&&t.push({rel:"stylesheet",href:r.rendererContext.buildAssetsURL(o.file),crossorigin:""});t.length&&n.head.push({link:t},{mode:"server"})}const i={};for(const e of n.head.entries.values())for(const[t,o]of Object.entries(resolveUnrefHeadInput(e.input))){const e=i[t];Array.isArray(e)&&e.push(...o),i[t]=o}const c={id:o.id,head:i,html:getServerComponentHTML(s.html),components:getClientIslandResponse(n),slots:getSlotIslandResponse(n)};return await t.hooks.callHook("render:island",c,{event:e,islandContext:o}),c});const _lazy_85ovE_=()=>Promise.resolve().then(function(){return po}),Kt=[{route:"",handler:Tt,lazy:!1,middleware:!0,method:void 0},{route:"/__nuxt_error",handler:_lazy_85ovE_,lazy:!0,middleware:!1,method:void 0},{route:"/api/_nuxt_icon/:collection",handler:$t,lazy:!1,middleware:!1,method:void 0},{route:"/__nuxt_island/**",handler:Qt,lazy:!1,middleware:!1,method:void 0},{route:"/_ipx/**",handler:k(()=>{const e=useRuntimeConfig().ipx||{},t=e?.fs?.dir?(Array.isArray(e.fs.dir)?e.fs.dir:[e.fs.dir]).map(e=>Te(e)?e:ke(new URL(e,globalThis._importMeta_.url))):void 0,o=e.fs?.dir?He({...e.fs,dir:t}):void 0,n=e.http?.domains?Ie({...e.http}):void 0;if(!o&&!n)throw new Error("IPX storage is not configured!");const r={...e,storage:o||n,httpStorage:n},s=Ve(r),a=ze(s);return E(e.baseURL,a)}),lazy:!1,middleware:!1,method:void 0},{route:"/**",handler:_lazy_85ovE_,lazy:!0,middleware:!1,method:void 0}];const Xt=function(){const e=useRuntimeConfig(),t=Q(),captureError=(e,o={})=>{const n=t.callHookParallel("error",e,o).catch(e=>{console.error("Error while capturing another error",e)});if(o.event&&i(o.event)){const t=o.event.context.nitro?.errors;t&&t.push({error:e,context:o}),o.event.waitUntil&&o.event.waitUntil(n)}},o=j({debug:W(!0),onError:(e,t)=>(captureError(e,{event:t,tags:["request"]}),async function(e,t){for(const o of mt)try{if(await o(e,t,{defaultHandler:defaultHandler}),t.handled)return}catch(e){console.error(e)}}(e,t)),onRequest:async e=>{e.context.nitro=e.context.nitro||{errors:[]};const t=e.node.req?.__unenv__;t?._platform&&(e.context={_platform:t?._platform,...t._platform,...e.context}),!e.context.waitUntil&&t?.waitUntil&&(e.context.waitUntil=t.waitUntil),e.fetch=(t,o)=>a(e,t,o,{fetch:localFetch}),e.$fetch=(t,o)=>a(e,t,o,{fetch:s}),e.waitUntil=t=>{e.context.nitro._waitUntilPromises||(e.context.nitro._waitUntilPromises=[]),e.context.nitro._waitUntilPromises.push(t),e.context.waitUntil&&e.context.waitUntil(t)},e.captureError=(t,o)=>{captureError(t,{event:e,...o})},await Xt.hooks.callHook("request",e).catch(t=>{captureError(t,{event:e,tags:["request"]})})},onBeforeResponse:async(e,t)=>{await Xt.hooks.callHook("beforeResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})},onAfterResponse:async(e,t)=>{await Xt.hooks.callHook("afterResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})}}),n=S({preemptive:!0}),r=T(o),localFetch=(e,t)=>e.toString().startsWith("/")?G(r,e,t).then(e=>function(e){return e.headers.has("set-cookie")?new Response(e.body,{status:e.status,statusText:e.statusText,headers:normalizeCookieHeaders(e.headers)}):e}(e)):globalThis.fetch(e,t),s=K({fetch:localFetch,Headers:X,defaults:{baseURL:e.app.baseURL}});var p;globalThis.$fetch=s,o.use((p={localFetch:localFetch},c(e=>{const t=getRouteRules(e);if(t.headers&&l(e,t.headers),t.redirect){let o=t.redirect.to;if(o.endsWith("/**")){let n=e.path;const r=t.redirect._redirectStripBase;r&&(n=withoutBase(n,r)),o=joinURL(o.slice(0,-3),n)}else e.path.includes("?")&&(o=withQuery(o,getQuery(e.path)));return d(e,o,t.redirect.statusCode)}if(t.proxy){let o=t.proxy.to;if(o.endsWith("/**")){let n=e.path;const r=t.proxy._proxyStripBase;r&&(n=withoutBase(n,r)),o=joinURL(o.slice(0,-3),n)}else e.path.includes("?")&&(o=withQuery(o,getQuery(e.path)));return u(e,o,{fetch:p.localFetch,...t.proxy})}})));for(const t of Kt){let r=t.lazy?k(t.handler):t.handler;if(t.middleware||!t.route){const n=(e.app.baseURL+(t.route||"/")).replace(/\/+/g,"/");o.use(n,r)}else{const e=getRouteRulesForPath(t.route.replace(/:\w+|\*\*/g,"_"));e.cache&&(r=it(r,{group:"nitro/routes",...e.cache})),n.use(t.route,r,t.method)}}return o.use(e.app.baseURL,n.handler),{hooks:t,h3App:o,router:n,localCall:e=>J(r,e),localFetch:localFetch,captureError:captureError}}();function useNitroApp(){return Xt}!function(e){for(const t of Rt)try{t(e)}catch(t){throw e.captureError(t,{tags:["plugin"]}),t}}(Xt),globalThis.crypto||(globalThis.crypto=D);const{NITRO_NO_UNIX_SOCKET:Gt,NITRO_DEV_WORKER_ID:Jt}=e.env;e.on("unhandledRejection",e=>_captureError(e,"unhandledRejection")),e.on("uncaughtException",e=>_captureError(e,"uncaughtException")),H?.on("message",e=>{e&&"shutdown"===e.event&&shutdown()});const Yt=useNitroApp(),Zt=new L(T(Yt.h3App));let eo;function listen(o=Boolean(Gt||e.versions.webcontainer||"Bun"in globalThis&&"win32"===e.platform)){return new Promise((n,r)=>{try{eo=Zt.listen(o?0:function(){const o=`nitro-worker-${e.pid}-${I}-${Jt}-${Math.round(1e4*Math.random())}.sock`;if("win32"===e.platform)return P(String.raw`\\.\pipe`,o);if("linux"===e.platform){if(Number.parseInt(e.versions.node.split(".")[0],10)>=20)return`\0${o}`}return P(t(),o)}(),()=>{const e=Zt.address();H?.postMessage({event:"listen",address:"string"==typeof e?{socketPath:e}:{host:"localhost",port:e?.port}}),n()})}catch(e){r(e)}})}async function shutdown(){Zt.closeAllConnections?.(),await Promise.all([new Promise(e=>eo?.close(e)),Yt.hooks.callHook("close").catch(console.error)]),H?.postMessage({event:"exit"})}listen().catch(()=>listen(!0)).catch(e=>(console.error("Dev worker failed to listen:",e),shutdown())),Yt.router.get("/_nitro/tasks",o(async e=>{const t=await Promise.all(Object.entries(Ut).map(async([e,t])=>{const o=await(t.resolve?.());return[e,{description:o?.meta?.description}]}));return{tasks:Object.fromEntries(t),scheduledTasks:false}})),Yt.router.use("/_nitro/tasks/:name",o(async e=>{const t=U(e,"name"),o={...C(e),...await R(e).then(e=>e?.payload).catch(()=>({}))};return await async function(e,{payload:t={},context:o={}}={}){if(Ot[e])return Ot[e];if(!(e in Ut))throw _({message:`Task \`${e}\` is not available!`,statusCode:404});if(!Ut[e].resolve)throw _({message:`Task \`${e}\` is not implemented!`,statusCode:501});const n=await Ut[e].resolve(),r={name:e,payload:t,context:o};Ot[e]=n.run(r);try{return await Ot[e]}finally{delete Ot[e]}}(t,{payload:o})}));const to={appName:"Nuxt",statusCode:500,statusMessage:"Server error",description:"An error occurred in the application and the page could not be served.",stack:""},oo=Object.freeze(Object.defineProperty({__proto__:null,template:e=>(e={...to,...e},'<!DOCTYPE html><html lang="en"><head><title>'+V(e.statusCode)+" - "+V(e.statusMessage||"Internal Server Error")+'</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll(\'link[rel="modulepreload"]\'))r(e);new MutationObserver(e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)}).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script><style>*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}h1,p{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.absolute{position:absolute}.top-6{top:1.5rem}.z-10{z-index:10}.mx-auto{margin-left:auto;margin-right:auto}.mb-4{margin-bottom:1rem}.mb-8{margin-bottom:2rem}.inline-block{display:inline-block}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.border{border-width:1px}.border-b-0{border-bottom-width:0}.border-black\\/5{border-color:#0000000d}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-gray-50\\/50{background-color:#f5f5f580}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-12{padding-top:3rem}.text-6xl{font-size:3.75rem;line-height:1}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.hover\\:text-\\[\\#00DC82\\]:hover{--un-text-opacity:1;color:rgb(0 220 130/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.hover\\:underline:hover{text-decoration-line:underline}.underline-offset-3{text-underline-offset:3px}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:border-white\\/10{border-color:#ffffff1a}.dark\\:bg-\\[\\#020420\\]{--un-bg-opacity:1;background-color:rgb(2 4 32/var(--un-bg-opacity))}.dark\\:bg-white\\/5{background-color:#ffffff0d}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:right-6{right:1.5rem}.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style></head><body class="antialiased bg-white dark:bg-[#020420] dark:text-white flex flex-col font-sans min-h-screen pt-12 px-10 text-black"><h1 class="font-medium mb-4 sm:text-8xl text-6xl">'+V(e.statusCode)+'</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">'+V(e.description)+'</p><a href="https://nuxt.com/docs/getting-started/error-handling?utm_source=nuxt-error-dev-page" target="_blank" class="absolute font-medium hover:text-[#00DC82] hover:underline inline-block mx-auto sm:right-6 text-sm top-6 underline-offset-3">Customize this page</a><div class="bg-gray-50/50 border border-b-0 border-black/5 dark:bg-white/5 dark:border-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><div class="font-light leading-tight p-8 text-xl z-10">'+V(e.stack)+"</div></div></body></html>")},Symbol.toStringTag,{value:"Module"})),no=Object.freeze(Object.defineProperty({__proto__:null,template:""},Symbol.toStringTag,{value:"Module"})),ro=Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"}));function renderPayloadJsonScript(e){const t={type:"application/json",innerHTML:e.data?ye(e.data,e.ssrContext._payloadReducers):"","data-nuxt-data":xt,"data-ssr":!e.ssrContext.noSSR,id:"__NUXT_DATA__"};e.src&&(t["data-src"]=e.src);return[t,{innerHTML:`window.__NUXT__={};window.__NUXT__.config=${be(e.ssrContext.config)}`}]}function splitPayload(e){const{data:t,prerenderedAt:o,...n}=e.payload;return{initial:{...n,prerenderedAt:o},payload:{data:t,prerenderedAt:o}}}const so={omitLineBreaks:!0};globalThis.__buildAssetsURL=buildAssetsURL,globalThis.__publicAssetsURL=publicAssetsURL;const ao=!!bt.id,io=ao?`<div${Ne(bt)}>`:"",co=ao?"</div>":"",lo=/^[^?]*\/_payload.json(?:\?.*)?$/,uo=function(e){const t=useRuntimeConfig();return c(async o=>{const n=useNitroApp(),r={event:o,render:e,response:void 0};if(await n.hooks.callHook("render:before",r),!r.response){if(o.path===`${t.app.baseURL}favicon.ico`)return y(o,"Content-Type","image/x-icon"),m(o,"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");if(r.response=await r.render(o),!r.response){const e=A(o);return h(o,200===e?500:e),m(o,"No response returned from render handler: "+o.path)}}return await n.hooks.callHook("render:response",r.response,r),r.response.headers&&f(o,r.response.headers),(r.response.statusCode||r.response.statusMessage)&&h(o,r.response.statusCode,r.response.statusMessage),r.response.body})}(async e=>{const t=useNitroApp(),o=e.path.startsWith("/__nuxt_error")?C(e):null;if(o&&!("__unenv__"in e.node.req))throw _({statusCode:404,statusMessage:"Page Not Found: /__nuxt_error"});const n=createSSRContext(e),r={mode:"server"};if(n.head.push(gt,r),o){if(o.statusCode&&=Number.parseInt(o.statusCode),"string"==typeof o.data)try{o.data=W(o.data)}catch{}!function(e,t){e.error=!0,e.payload={error:t},e.url=t.url}(n,o)}const s=lo.test(n.url);if(s){const t=n.url.substring(0,n.url.lastIndexOf("/"))||"/";n.url=t,e._path=e.node.req.url=t}const a=getRouteRules(e);!1===a.ssr&&(n.noSSR=!0);const i=await function(e){return e.noSSR?Vt():It()}(n),c=await i.renderToString(n).catch(async e=>{if(n._renderResponse&&"skipping render"===e.message)return{};const t=!o&&n.payload?.error||e;throw await(n.nuxt?.hooks.callHook("app:error",t)),t}),l=[];if(await(n.nuxt?.hooks.callHook("app:rendered",{ssrContext:n,renderResult:c})),n._renderResponse)return n._renderResponse;if(n.payload?.error&&!o)throw n.payload.error;if(s){const e=function(e){return{body:ye(splitPayload(e).payload,e._payloadReducers),statusCode:A(e.event),statusMessage:O(e.event),headers:{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"}}}(n);return e}const d=a.noScripts,{styles:u,scripts:p}=M(n,i.rendererContext);n._preloadManifest&&!d&&n.head.push({link:[{rel:"preload",as:"fetch",fetchpriority:"low",crossorigin:"anonymous",href:buildAssetsURL(`builds/meta/${n.runtimeConfig.app.buildId}.json`)}]},{...r,tagPriority:"low"}),l.length&&n.head.push({style:l});const f=[];for(const e of Object.values(u))"inline"in getQuery(e.file)||f.push({rel:"stylesheet",href:i.rendererContext.buildAssetsURL(e.file),crossorigin:""});if(f.length&&n.head.push({link:f},r),d||(n.head.push({link:F(n,i.rendererContext)},r),n.head.push({link:B(n,i.rendererContext)},r),n.head.push({script:renderPayloadJsonScript({ssrContext:n,data:n.payload})},{...r,tagPosition:"bodyClose",tagPriority:"high"})),!a.noScripts){const e="head";n.head.push({script:Object.values(p).map(t=>({type:t.module?"module":null,src:i.rendererContext.buildAssetsURL(t.file),defer:!t.module||null,tagPosition:e,crossorigin:""}))},r)}const{headTags:h,bodyTags:m,bodyTagsOpen:g,htmlAttrs:y,bodyAttrs:b}=await $e(n.head,so),w={htmlAttrs:y?[y]:[],head:normalizeChunks([h]),bodyAttrs:b?[b]:[],bodyPrepend:normalizeChunks([g,n.teleports?.body]),body:[replaceIslandTeleports(n,c.html),io+(ao?joinTags([n.teleports?.[`#${bt.id}`]]):"")+co],bodyAppend:[m]};return await t.hooks.callHook("render:html",w,{event:e}),{body:(x=w,`<!DOCTYPE html><html${joinAttrs(x.htmlAttrs)}><head>${joinTags(x.head)}</head><body${joinAttrs(x.bodyAttrs)}>${joinTags(x.bodyPrepend)}${joinTags(x.body)}${joinTags(x.bodyAppend)}</body></html>`),statusCode:A(e),statusMessage:O(e),headers:{"content-type":"text/html;charset=utf-8","x-powered-by":"Nuxt"}};var x});function normalizeChunks(e){return e.filter(Boolean).map(e=>e.trim())}function joinTags(e){return e.join("")}function joinAttrs(e){return 0===e.length?"":" "+e.join(" ")}const po=Object.freeze(Object.defineProperty({__proto__:null,default:uo},Symbol.toStringTag,{value:"Module"}));
//# sourceMappingURL=index.mjs.map
