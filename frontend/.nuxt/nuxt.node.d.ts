/// <reference types="woonuxt-settings" />
/// <reference types="nuxt-graphql-client" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxt/image" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference path="types/modules.d.ts" />
/// <reference path="types/runtime-config.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />

export {}
