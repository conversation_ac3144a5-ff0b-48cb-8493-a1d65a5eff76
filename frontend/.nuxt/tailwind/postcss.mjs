// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/8/6 03:45:08
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg4 from "./../../woonuxt_base/tailwind.config.ts";
const config = [
{"content":{"files":["/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","./components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/plugins/**/*.{js,ts,mjs}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/composables/**/*.{js,ts,mjs}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/utils/**/*.{js,ts,mjs}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/app.config.{js,ts,mjs}"]}},
{},
{"content":{"files":["/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/plugins/**/*.{js,ts,mjs}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/composables/**/*.{js,ts,mjs}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/utils/**/*.{js,ts,mjs}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/home/<USER>/桌面/pet-ecommerce-store-V14.0/frontend/woonuxt_base/app/app.config.{js,ts,mjs}"]}},
cfg4
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;