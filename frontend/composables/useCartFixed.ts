/**
 * 修复版本的购物车 Composable
 * 提供更好的错误处理和 CORS 支持
 */
export function useCartFixed() {
  const cart = useState<Cart | null>('cart', () => null);
  const isShowingCart = useState<boolean>('isShowingCart', () => false);
  const isUpdatingCart = useState<boolean>('isUpdatingCart', () => false);
  const isUpdatingCoupon = useState<boolean>('isUpdatingCoupon', () => false);
  const paymentGateways = useState<PaymentGateways | null>('paymentGateways', () => null);
  
  const { handleCartError, withRetry } = useErrorHandler();

  /**
   * 刷新购物车数据
   * @returns Promise<boolean> - 是否成功刷新
   */
  async function refreshCart(): Promise<boolean> {
    isUpdatingCart.value = true;
    
    try {
      // 使用重试机制
      const result = await withRetry(async () => {
        const { cart, customer, viewer, paymentGateways, loginClients } = await GqlGetCart();
        return { cart, customer, viewer, paymentGateways, loginClients };
      }, 2, 1000);

      const { cart: cartData, customer, viewer, paymentGateways: gateways, loginClients } = result;
      const { updateCustomer, updateViewer, updateLoginClients } = useAuth();

      if (cartData) updateCart(cartData);
      if (customer) updateCustomer(customer);
      if (viewer) updateViewer(viewer);
      if (gateways) updatePaymentGateways(gateways);
      if (loginClients) updateLoginClients(loginClients.filter((client) => client !== null));

      return true;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error refreshing cart:', errorMsg);
      
      // 如果是网络错误，不清除所有数据
      if (!error.message?.includes('fetch') && !error.message?.includes('CORS')) {
        clearAllCookies();
        resetInitialState();
      }
      
      return false;
    } finally {
      isUpdatingCart.value = false;
    }
  }

  /**
   * 重置初始状态
   */
  function resetInitialState() {
    cart.value = null;
    paymentGateways.value = null;
  }

  /**
   * 更新购物车数据
   * @param payload - 购物车数据
   */
  function updateCart(payload?: Cart | null): void {
    cart.value = payload || null;
  }

  /**
   * 更新支付网关
   * @param payload - 支付网关数据
   */
  function updatePaymentGateways(payload?: PaymentGateways | null): void {
    paymentGateways.value = payload || null;
  }

  /**
   * 添加商品到购物车
   * @param product - 商品对象
   * @param quantity - 数量
   * @param variation - 变体信息
   */
  async function addToCart(
    product: Product, 
    quantity: number = 1, 
    variation?: ProductVariation
  ): Promise<boolean> {
    isUpdatingCart.value = true;
    
    try {
      const input = {
        productId: product.databaseId,
        quantity,
        variationId: variation?.databaseId,
      };

      const result = await withRetry(async () => {
        return await GqlAddToCart({ input });
      }, 2, 500);

      if (result.addToCart?.cart) {
        updateCart(result.addToCart.cart);
        
        // 显示成功消息
        const { $toast } = useNuxtApp();
        if ($toast) {
          $toast.success(`${product.name} 已加入购物车`);
        }
        
        return true;
      }
      
      return false;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error adding to cart:', errorMsg);
      
      // 显示错误消息
      const { $toast } = useNuxtApp();
      if ($toast) {
        $toast.error(errorMsg);
      }
      
      return false;
    } finally {
      isUpdatingCart.value = false;
    }
  }

  /**
   * 从购物车移除商品
   * @param key - 购物车项目键
   */
  async function removeFromCart(key: string): Promise<boolean> {
    isUpdatingCart.value = true;
    
    try {
      const result = await withRetry(async () => {
        return await GqlRemoveItemsFromCart({ keys: [key] });
      }, 2, 500);

      if (result.removeItemsFromCart?.cart) {
        updateCart(result.removeItemsFromCart.cart);
        return true;
      }
      
      return false;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error removing from cart:', errorMsg);
      
      const { $toast } = useNuxtApp();
      if ($toast) {
        $toast.error(errorMsg);
      }
      
      return false;
    } finally {
      isUpdatingCart.value = false;
    }
  }

  /**
   * 更新购物车商品数量
   * @param key - 购物车项目键
   * @param quantity - 新数量
   */
  async function updateCartItemQuantity(key: string, quantity: number): Promise<boolean> {
    isUpdatingCart.value = true;
    
    try {
      const result = await withRetry(async () => {
        return await GqlUpdateItemQuantities({
          items: [{ key, quantity }]
        });
      }, 2, 500);

      if (result.updateItemQuantities?.cart) {
        updateCart(result.updateItemQuantities.cart);
        return true;
      }
      
      return false;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error updating cart item quantity:', errorMsg);
      
      const { $toast } = useNuxtApp();
      if ($toast) {
        $toast.error(errorMsg);
      }
      
      return false;
    } finally {
      isUpdatingCart.value = false;
    }
  }

  /**
   * 清空购物车
   */
  async function clearCart(): Promise<boolean> {
    isUpdatingCart.value = true;
    
    try {
      const result = await withRetry(async () => {
        return await GqlEmptyCart();
      }, 2, 500);

      if (result.emptyCart?.cart) {
        updateCart(result.emptyCart.cart);
        return true;
      }
      
      return false;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error clearing cart:', errorMsg);
      
      const { $toast } = useNuxtApp();
      if ($toast) {
        $toast.error(errorMsg);
      }
      
      return false;
    } finally {
      isUpdatingCart.value = false;
    }
  }

  /**
   * 应用优惠券
   * @param code - 优惠券代码
   */
  async function applyCoupon(code: string): Promise<boolean> {
    isUpdatingCoupon.value = true;
    
    try {
      const result = await withRetry(async () => {
        return await GqlApplyCoupon({ code });
      }, 2, 500);

      if (result.applyCoupon?.cart) {
        updateCart(result.applyCoupon.cart);
        
        const { $toast } = useNuxtApp();
        if ($toast) {
          $toast.success('优惠券应用成功');
        }
        
        return true;
      }
      
      return false;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error applying coupon:', errorMsg);
      
      const { $toast } = useNuxtApp();
      if ($toast) {
        $toast.error(errorMsg);
      }
      
      return false;
    } finally {
      isUpdatingCoupon.value = false;
    }
  }

  /**
   * 移除优惠券
   * @param code - 优惠券代码
   */
  async function removeCoupon(code: string): Promise<boolean> {
    isUpdatingCoupon.value = true;
    
    try {
      const result = await withRetry(async () => {
        return await GqlRemoveCoupons({ codes: [code] });
      }, 2, 500);

      if (result.removeCoupons?.cart) {
        updateCart(result.removeCoupons.cart);
        return true;
      }
      
      return false;
    } catch (error: any) {
      const errorMsg = handleCartError(error);
      console.error('Error removing coupon:', errorMsg);
      
      const { $toast } = useNuxtApp();
      if ($toast) {
        $toast.error(errorMsg);
      }
      
      return false;
    } finally {
      isUpdatingCoupon.value = false;
    }
  }

  // 计算属性
  const cartCount = computed(() => {
    return cart.value?.contents?.itemCount || 0;
  });

  const cartTotal = computed(() => {
    return cart.value?.total || '¥0.00';
  });

  const cartSubtotal = computed(() => {
    return cart.value?.subtotal || '¥0.00';
  });

  const cartItems = computed(() => {
    return cart.value?.contents?.nodes || [];
  });

  const isEmpty = computed(() => {
    return cartCount.value === 0;
  });

  // 清除所有 cookies 的辅助函数
  function clearAllCookies() {
    // 实现清除 cookies 的逻辑
    if (process.client) {
      document.cookie.split(";").forEach((c) => {
        document.cookie = c
          .replace(/^ +/, "")
          .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
    }
  }

  return {
    // 状态
    cart: readonly(cart),
    isShowingCart,
    isUpdatingCart: readonly(isUpdatingCart),
    isUpdatingCoupon: readonly(isUpdatingCoupon),
    paymentGateways: readonly(paymentGateways),
    
    // 计算属性
    cartCount,
    cartTotal,
    cartSubtotal,
    cartItems,
    isEmpty,
    
    // 方法
    refreshCart,
    updateCart,
    updatePaymentGateways,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    applyCoupon,
    removeCoupon,
    resetInitialState,
  };
}
