/**
 * 自定义错误处理 Composable
 * 提供更好的错误处理和用户友好的错误消息
 */
export function useErrorHandler() {
  /**
   * 提取并格式化错误消息
   * @param error - 任何类型的错误对象
   * @returns 用户友好的错误消息
   */
  const getErrorMessage = (error: any): string => {
    // 如果没有错误，返回默认消息
    if (!error) {
      return '发生了未知错误，请稍后重试';
    }

    // GraphQL 错误
    if (error.gqlErrors && error.gqlErrors.length > 0) {
      return error.gqlErrors[0].message || '服务器响应错误';
    }

    // 网络错误
    if (error.networkError) {
      if (error.networkError.statusCode === 404) {
        return '请求的资源不存在';
      }
      if (error.networkError.statusCode === 500) {
        return '服务器内部错误，请稍后重试';
      }
      if (error.networkError.statusCode === 403) {
        return '没有权限访问此资源';
      }
      return `网络错误: ${error.networkError.message || '连接失败'}`;
    }

    // Fetch 错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return '网络连接失败，请检查网络连接';
    }

    // CORS 错误
    if (error.message && error.message.includes('CORS')) {
      return '跨域请求被阻止，请联系管理员';
    }

    // 标准错误对象
    if (error.message) {
      return error.message;
    }

    // 字符串错误
    if (typeof error === 'string') {
      return error;
    }

    // 其他情况
    return '发生了未知错误，请稍后重试';
  };

  /**
   * 处理 GraphQL 查询错误
   * @param error - GraphQL 错误
   * @param context - 错误上下文信息
   */
  const handleGraphQLError = (error: any, context?: string) => {
    const message = getErrorMessage(error);
    const contextMessage = context ? `${context}: ${message}` : message;
    
    console.error('GraphQL Error:', {
      context,
      error,
      message: contextMessage
    });

    // 在开发环境显示详细错误
    if (process.dev) {
      console.error('Detailed error:', error);
    }

    return contextMessage;
  };

  /**
   * 处理购物车相关错误
   * @param error - 错误对象
   * @returns 用户友好的错误消息
   */
  const handleCartError = (error: any): string => {
    const message = getErrorMessage(error);
    
    // 特定的购物车错误处理
    if (message.includes('out of stock')) {
      return '商品库存不足';
    }
    if (message.includes('invalid product')) {
      return '商品信息无效';
    }
    if (message.includes('cart is empty')) {
      return '购物车为空';
    }
    
    return handleGraphQLError(error, '购物车操作');
  };

  /**
   * 处理用户认证错误
   * @param error - 错误对象
   * @returns 用户友好的错误消息
   */
  const handleAuthError = (error: any): string => {
    const message = getErrorMessage(error);
    
    // 特定的认证错误处理
    if (message.includes('invalid credentials')) {
      return '用户名或密码错误';
    }
    if (message.includes('user not found')) {
      return '用户不存在';
    }
    if (message.includes('token expired')) {
      return '登录已过期，请重新登录';
    }
    if (message.includes('unauthorized')) {
      return '没有权限执行此操作';
    }
    
    return handleGraphQLError(error, '用户认证');
  };

  /**
   * 处理产品相关错误
   * @param error - 错误对象
   * @returns 用户友好的错误消息
   */
  const handleProductError = (error: any): string => {
    const message = getErrorMessage(error);
    
    // 特定的产品错误处理
    if (message.includes('product not found')) {
      return '商品不存在或已下架';
    }
    if (message.includes('invalid variation')) {
      return '商品规格无效';
    }
    
    return handleGraphQLError(error, '商品操作');
  };

  /**
   * 显示错误通知
   * @param error - 错误对象
   * @param type - 错误类型
   */
  const showErrorNotification = (error: any, type: 'cart' | 'auth' | 'product' | 'general' = 'general') => {
    let message: string;
    
    switch (type) {
      case 'cart':
        message = handleCartError(error);
        break;
      case 'auth':
        message = handleAuthError(error);
        break;
      case 'product':
        message = handleProductError(error);
        break;
      default:
        message = getErrorMessage(error);
    }

    // 使用 Nuxt 的通知系统（如果可用）
    const { $toast } = useNuxtApp();
    if ($toast) {
      $toast.error(message);
    } else {
      // 回退到 console.error
      console.error(message);
    }
  };

  /**
   * 重试机制
   * @param fn - 要重试的函数
   * @param maxRetries - 最大重试次数
   * @param delay - 重试延迟（毫秒）
   */
  const withRetry = async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: any;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i === maxRetries) {
          throw error;
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
    
    throw lastError;
  };

  return {
    getErrorMessage,
    handleGraphQLError,
    handleCartError,
    handleAuthError,
    handleProductError,
    showErrorNotification,
    withRetry
  };
}
