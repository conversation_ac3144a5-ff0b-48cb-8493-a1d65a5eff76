/**
 * 高级购物车组合式函数
 * 展示 Pinia + Composition API 的最佳实践
 */

export const useAdvancedCart = () => {
  // 检查是否在客户端
  if (import.meta.server) {
    // 服务端返回默认值
    return {
      isProcessing: ref(false),
      cartSummary: computed(() => ({
        itemCount: 0,
        subtotal: '0',
        total: '0',
        isEmpty: true,
        hasItems: false
      })),
      cartItems: computed(() => []),
      cartTotal: computed(() => ({
        subtotal: '0.00',
        shipping: '0.00',
        tax: '0.00',
        total: '0.00'
      })),
      cartAnalytics: computed(() => ({
        uniqueProducts: 0,
        totalItems: 0,
        categories: [],
        averageItemValue: '0',
        totalValue: '0.00'
      })),
      addToCartWithFeedback: async () => {},
      updateQuantityWithOptimism: async () => {},
      removeItemWithConfirmation: async () => false,
      clearCartWithConfirmation: async () => false,
      addMultipleItems: async () => ({ results: [], errors: [] }),
      saveCartToWishlist: async () => false,
      restoreSavedCart: async () => false,
      clearOptimisticUpdates: () => {}
    }
  }

  const cartStore = useCartStore()

  // === 响应式状态 ===
  const isProcessing = ref(false)
  const optimisticUpdates = ref<Map<string, any>>(new Map())

  // === 计算属性 ===
  const cartSummary = computed(() => ({
    itemCount: cartStore.itemCount || 0,
    subtotal: cartStore.subtotal || '0',
    total: cartStore.total || '0',
    isEmpty: cartStore.isEmpty ?? true,
    hasItems: !(cartStore.isEmpty ?? true)
  }))

  const cartItems = computed(() => {
    const items = cartStore.items || []
    return items.map(item => ({
      ...item,
      // 应用乐观更新
      quantity: optimisticUpdates.value.get(item.key)?.quantity ?? item.quantity,
      isUpdating: optimisticUpdates.value.has(item.key)
    }))
  })

  const cartTotal = computed(() => {
    const subtotalStr = cartStore.subtotal || '0'
    const subtotal = parseFloat(subtotalStr.replace(/[^\d.]/g, '') || '0')
    const shipping = 0 // 可以根据需要计算运费
    const tax = subtotal * 0.1 // 假设10%税率
    return {
      subtotal: subtotal.toFixed(2),
      shipping: shipping.toFixed(2),
      tax: tax.toFixed(2),
      total: (subtotal + shipping + tax).toFixed(2)
    }
  })

  // === 乐观更新辅助函数 ===
  const addOptimisticUpdate = (key: string, update: any) => {
    optimisticUpdates.value.set(key, update)
  }

  const removeOptimisticUpdate = (key: string) => {
    optimisticUpdates.value.delete(key)
  }

  const clearOptimisticUpdates = () => {
    optimisticUpdates.value.clear()
  }

  // === 高级购物车操作 ===
  const addToCartWithFeedback = async (
    productId: string,
    quantity = 1,
    options: {
      showNotification?: boolean
      optimistic?: boolean
      variation?: any
    } = {}
  ) => {
    const { showNotification = true, optimistic = true, variation } = options

    if (optimistic) {
      // 乐观更新：立即显示变化
      const tempKey = `temp-${productId}-${Date.now()}`
      addOptimisticUpdate(tempKey, { productId, quantity, variation })
    }

    try {
      isProcessing.value = true
      const result = await cartStore.addToCart(productId, quantity, variation)
      
      if (showNotification && import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.success('成功', `已添加 ${quantity} 件商品到购物车`)
      }

      return result
    } catch (error) {
      if (showNotification && import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.error('失败', '添加到购物车失败，请重试')
      }
      throw error
    } finally {
      isProcessing.value = false
      clearOptimisticUpdates()
    }
  }

  const updateQuantityWithOptimism = async (key: string, newQuantity: number) => {
    // 乐观更新
    addOptimisticUpdate(key, { quantity: newQuantity })

    try {
      await cartStore.updateQuantity(key, newQuantity)
    } catch (error) {
      // 回滚乐观更新
      removeOptimisticUpdate(key)
      throw error
    } finally {
      removeOptimisticUpdate(key)
    }
  }

  const removeItemWithConfirmation = async (
    key: string,
    options: { requireConfirmation?: boolean } = {}
  ) => {
    const { requireConfirmation = true } = options

    if (requireConfirmation) {
      const confirmed = await new Promise<boolean>((resolve) => {
        // 这里可以集成一个确认对话框
        const result = confirm('确定要从购物车中移除这件商品吗？')
        resolve(result)
      })

      if (!confirmed) {
        return false
      }
    }

    try {
      await cartStore.removeFromCart(key)
      return true
    } catch (error) {
      if (import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.error('失败', '移除商品失败，请重试')
      }
      return false
    }
  }

  // === 批量操作 ===
  const addMultipleItems = async (
    items: Array<{
      productId: string
      quantity: number
      variation?: any
    }>
  ) => {
    isProcessing.value = true
    const results = []
    const errors = []

    for (const item of items) {
      try {
        const result = await cartStore.addToCart(
          item.productId,
          item.quantity,
          item.variation
        )
        results.push(result)
      } catch (error) {
        errors.push({ item, error })
      }
    }

    isProcessing.value = false

    if (errors.length > 0 && import.meta.client) {
      const { useNotifications } = await import('~/stores/ui')
      const notifications = useNotifications()
      notifications.warning(
        '部分商品添加失败',
        `${errors.length} 件商品添加失败，${results.length} 件商品添加成功`
      )
    }

    return { results, errors }
  }

  const clearCartWithConfirmation = async () => {
    const confirmed = await new Promise<boolean>((resolve) => {
      const result = confirm('确定要清空购物车吗？此操作不可撤销。')
      resolve(result)
    })

    if (confirmed) {
      return cartStore.clearCart()
    }
    return false
  }

  // === 购物车分析 ===
  const getCartAnalytics = computed(() => {
    const items = cartStore.items || []
    const categories = new Set()
    let totalValue = 0

    items.forEach(item => {
      if (item.product?.productCategories?.nodes) {
        item.product.productCategories.nodes.forEach((cat: any) => {
          categories.add(cat.name)
        })
      }

      const price = parseFloat(item.product?.price?.replace(/[^\d.]/g, '') || '0')
      totalValue += price * (item.quantity || 0)
    })

    return {
      uniqueProducts: items.length,
      totalItems: cartStore.itemCount || 0,
      categories: Array.from(categories),
      averageItemValue: items.length > 0 ? (totalValue / items.length).toFixed(2) : '0',
      totalValue: totalValue.toFixed(2)
    }
  })

  // === 购物车持久化 ===
  const saveCartToWishlist = async () => {
    try {
      const cartData = {
        items: cartStore.items,
        timestamp: Date.now(),
        name: `购物车保存 - ${new Date().toLocaleDateString()}`
      }

      localStorage.setItem('saved-cart', JSON.stringify(cartData))

      if (import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.success('成功', '购物车已保存到心愿单')
      }
      
      return true
    } catch (error) {
      console.error('保存购物车失败:', error)
      return false
    }
  }

  const restoreSavedCart = async () => {
    try {
      const saved = localStorage.getItem('saved-cart')
      if (!saved) return false

      const { items } = JSON.parse(saved)
      
      for (const item of items) {
        await cartStore.addToCart(
          item.product.id,
          item.quantity,
          item.variation
        )
      }

      if (import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.success('成功', '已恢复保存的购物车')
      }
      
      return true
    } catch (error) {
      console.error('恢复购物车失败:', error)
      return false
    }
  }

  // === 生命周期和监听器 ===
  onMounted(() => {
    // 组件挂载时的初始化逻辑
    cartStore.refreshCart()
  })

  onUnmounted(() => {
    // 清理乐观更新
    clearOptimisticUpdates()
  })

  // 监听购物车变化
  watch(
    () => cartStore.itemCount,
    (newCount, oldCount) => {
      if (newCount > oldCount) {
        // 商品增加时的逻辑
        console.log('商品已添加到购物车')
      }
    }
  )

  return {
    // === 状态 ===
    isProcessing: readonly(isProcessing),
    cartSummary,
    cartItems,
    cartTotal,
    cartAnalytics: getCartAnalytics,

    // === 基础操作 ===
    addToCartWithFeedback,
    updateQuantityWithOptimism,
    removeItemWithConfirmation,
    clearCartWithConfirmation,

    // === 批量操作 ===
    addMultipleItems,

    // === 持久化 ===
    saveCartToWishlist,
    restoreSavedCart,

    // === 工具方法 ===
    clearOptimisticUpdates
  }
}
