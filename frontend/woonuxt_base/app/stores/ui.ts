import { defineStore } from 'pinia'

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
}

interface Modal {
  id: string
  component: string
  props?: Record<string, any>
  persistent?: boolean
}

export const useUIStore = defineStore('ui', () => {
  // 状态
  const isShowingMobileMenu = ref(false)
  const isShowingCart = ref(false)
  const isShowingSearch = ref(false)
  const isShowingFilters = ref(false)
  const notifications = ref<Notification[]>([])
  const modals = ref<Modal[]>([])
  const loading = ref<Record<string, boolean>>({})
  const theme = ref<'light' | 'dark' | 'auto'>('auto')

  // 动作
  const toggleMobileMenu = (show?: boolean) => {
    isShowingMobileMenu.value = show !== undefined ? show : !isShowingMobileMenu.value
  }

  const toggleCart = (show?: boolean) => {
    isShowingCart.value = show !== undefined ? show : !isShowingCart.value
  }

  const toggleSearch = (show?: boolean) => {
    isShowingSearch.value = show !== undefined ? show : !isShowingSearch.value
  }

  const toggleFilters = (show?: boolean) => {
    isShowingFilters.value = show !== undefined ? show : !isShowingFilters.value
  }

  // 通知管理
  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newNotification: Notification = {
      id,
      duration: 5000,
      ...notification
    }
    
    notifications.value.push(newNotification)
    
    // 自动移除通知（除非是持久化的）
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }
    
    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearNotifications = () => {
    notifications.value = []
  }

  // 模态框管理
  const openModal = (modal: Omit<Modal, 'id'>) => {
    const id = `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newModal: Modal = { id, ...modal }
    
    modals.value.push(newModal)
    
    return id
  }

  const closeModal = (id: string) => {
    const index = modals.value.findIndex(m => m.id === id)
    if (index > -1) {
      modals.value.splice(index, 1)
    }
  }

  const closeAllModals = () => {
    modals.value = []
  }

  // 加载状态管理
  const setLoading = (key: string, isLoading: boolean) => {
    loading.value[key] = isLoading
  }

  const isLoading = (key: string) => {
    return loading.value[key] || false
  }

  // 主题管理
  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme
    
    // 应用主题到DOM
    if (import.meta.client) {
      const root = document.documentElement
      if (newTheme === 'dark') {
        root.classList.add('dark')
      } else if (newTheme === 'light') {
        root.classList.remove('dark')
      } else {
        // auto - 根据系统偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        if (prefersDark) {
          root.classList.add('dark')
        } else {
          root.classList.remove('dark')
        }
      }
    }
  }

  // 工具方法
  const closeAllOverlays = () => {
    isShowingMobileMenu.value = false
    isShowingCart.value = false
    isShowingSearch.value = false
    isShowingFilters.value = false
    modals.value = []
  }

  return {
    // 状态
    isShowingMobileMenu: readonly(isShowingMobileMenu),
    isShowingCart: readonly(isShowingCart),
    isShowingSearch: readonly(isShowingSearch),
    isShowingFilters: readonly(isShowingFilters),
    notifications: readonly(notifications),
    modals: readonly(modals),
    loading: readonly(loading),
    theme: readonly(theme),
    
    // 动作
    toggleMobileMenu,
    toggleCart,
    toggleSearch,
    toggleFilters,
    addNotification,
    removeNotification,
    clearNotifications,
    openModal,
    closeModal,
    closeAllModals,
    setLoading,
    isLoading,
    setTheme,
    closeAllOverlays
  }
})

// 便捷的通知方法
export const useNotifications = () => {
  const uiStore = useUIStore()
  
  return {
    success: (title: string, message?: string) => 
      uiStore.addNotification({ type: 'success', title, message }),
    
    error: (title: string, message?: string) => 
      uiStore.addNotification({ type: 'error', title, message, duration: 8000 }),
    
    warning: (title: string, message?: string) => 
      uiStore.addNotification({ type: 'warning', title, message }),
    
    info: (title: string, message?: string) => 
      uiStore.addNotification({ type: 'info', title, message })
  }
}
