import { defineStore } from 'pinia'
import type { Cart, CartItem } from '#woo'

interface CartState {
  cart: Cart | null
  isLoading: boolean
  isShowingCart: boolean
  lastError: string | null
}

export const useCartStore = defineStore('cart', () => {
  // === 响应式状态 ===
  const cart = ref<Cart | null>(null)
  const isLoading = ref(false)
  const isShowingCart = ref(false)
  const lastError = ref<string | null>(null)

  // === 计算属性 ===
  const itemCount = computed(() => {
    return cart.value?.contents?.itemCount || 0
  })

  const subtotal = computed(() => {
    return cart.value?.subtotal || '0'
  })

  const total = computed(() => {
    return cart.value?.total || '0'
  })

  const isEmpty = computed(() => {
    return itemCount.value === 0
  })

  const items = computed(() => {
    return cart.value?.contents?.nodes || []
  })

  // 获取特定商品的数量
  const getItemQuantity = computed(() => {
    return (productId: string) => {
      const item = items.value.find(item => item.product?.id === productId)
      return item?.quantity || 0
    }
  })

  // 检查商品是否在购物车中
  const hasItem = computed(() => {
    return (productId: string) => {
      return items.value.some(item => item.product?.id === productId)
    }
  })

  // === 内部辅助函数 ===
  const setError = (error: string | null) => {
    lastError.value = error
  }

  const handleAsyncAction = async <T>(
    action: () => Promise<T>,
    errorMessage: string = '操作失败'
  ): Promise<T | null> => {
    isLoading.value = true
    setError(null)

    try {
      const result = await action()
      return result
    } catch (error) {
      console.error(errorMessage, error)
      setError(error instanceof Error ? error.message : errorMessage)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // === 基础动作 ===
  const setCart = (newCart: Cart | null) => {
    cart.value = newCart
    setError(null)
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const toggleCart = (show?: boolean) => {
    isShowingCart.value = show !== undefined ? show : !isShowingCart.value
  }

  const clearError = () => {
    setError(null)
  }

  // === 高级购物车操作 ===
  const addToCart = async (
    productId: string,
    quantity = 1,
    variation: any = null
  ) => {
    return handleAsyncAction(async () => {
      const { addToCart: addToCartFn } = useCart()
      const result = await addToCartFn(productId, quantity, variation)

      // 刷新购物车数据
      await refreshCart()

      // 触发成功通知
      if (import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.success('成功', `已添加 ${quantity} 件商品到购物车`)
      }

      return result
    }, '添加到购物车失败')
  }

  // 批量添加商品
  const addMultipleToCart = async (items: Array<{
    productId: string
    quantity: number
    variation?: any
  }>) => {
    return handleAsyncAction(async () => {
      const results = []
      for (const item of items) {
        const result = await addToCart(item.productId, item.quantity, item.variation)
        results.push(result)
      }
      return results
    }, '批量添加商品失败')
  }

  const removeFromCart = async (key: string) => {
    return handleAsyncAction(async () => {
      const { removeItem } = useCart()
      await removeItem(key)
      await refreshCart()

      if (import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.success('成功', '商品已从购物车移除')
      }
    }, '从购物车移除失败')
  }

  const updateQuantity = async (key: string, quantity: number) => {
    return handleAsyncAction(async () => {
      if (quantity <= 0) {
        return removeFromCart(key)
      }

      const { updateItemQuantity } = useCart()
      await updateItemQuantity(key, quantity)
      await refreshCart()
    }, '更新数量失败')
  }

  // 增加商品数量
  const incrementQuantity = async (key: string) => {
    const item = items.value.find(item => item.key === key)
    if (item) {
      return updateQuantity(key, (item.quantity || 0) + 1)
    }
  }

  // 减少商品数量
  const decrementQuantity = async (key: string) => {
    const item = items.value.find(item => item.key === key)
    if (item && item.quantity && item.quantity > 1) {
      return updateQuantity(key, item.quantity - 1)
    } else if (item) {
      return removeFromCart(key)
    }
  }

  const clearCart = async () => {
    return handleAsyncAction(async () => {
      const { emptyCart } = useCart()
      await emptyCart()
      await refreshCart()

      if (import.meta.client) {
        const { useNotifications } = await import('~/stores/ui')
        const notifications = useNotifications()
        notifications.success('成功', '购物车已清空')
      }
    }, '清空购物车失败')
  }

  const refreshCart = async () => {
    return handleAsyncAction(async () => {
      const { refreshCart: refresh } = useCart()
      const newCart = await refresh()
      cart.value = newCart
      return newCart
    }, '刷新购物车失败')
  }

  // === 监听器和副作用 ===
  // 监听购物车变化，自动保存到localStorage
  watch(cart, (newCart) => {
    if (import.meta.client && newCart) {
      localStorage.setItem('woonuxt-cart', JSON.stringify({
        timestamp: Date.now(),
        data: newCart
      }))
    }
  }, { deep: true })

  // 从localStorage恢复购物车数据
  const restoreFromStorage = () => {
    if (import.meta.client) {
      try {
        const stored = localStorage.getItem('woonuxt-cart')
        if (stored) {
          const { timestamp, data } = JSON.parse(stored)
          // 只恢复24小时内的数据
          if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
            cart.value = data
          }
        }
      } catch (error) {
        console.warn('Failed to restore cart from storage:', error)
      }
    }
  }

  // 初始化时恢复数据
  if (import.meta.client) {
    restoreFromStorage()
  }

  return {
    // === 状态 ===
    cart: readonly(cart),
    isLoading: readonly(isLoading),
    isShowingCart: readonly(isShowingCart),
    lastError: readonly(lastError),

    // === 计算属性 ===
    itemCount,
    subtotal,
    total,
    isEmpty,
    items,
    getItemQuantity,
    hasItem,

    // === 基础动作 ===
    setCart,
    setLoading,
    toggleCart,
    clearError,

    // === 购物车操作 ===
    addToCart,
    addMultipleToCart,
    removeFromCart,
    updateQuantity,
    incrementQuantity,
    decrementQuantity,
    clearCart,
    refreshCart,

    // === 工具方法 ===
    restoreFromStorage
  }
})
