import { defineStore } from 'pinia'

interface ProductFilters {
  category?: string
  priceRange?: [number, number]
  attributes?: Record<string, string[]>
  onSale?: boolean
  inStock?: boolean
  rating?: number
  search?: string
}

interface ProductSort {
  field: 'date' | 'price' | 'popularity' | 'rating' | 'title'
  order: 'asc' | 'desc'
}

export const useProductStore = defineStore('product', () => {
  // === 响应式状态 ===
  const products = ref<any[]>([])
  const categories = ref<any[]>([])
  const currentProduct = ref<any>(null)
  const filters = ref<ProductFilters>({})
  const sort = ref<ProductSort>({ field: 'date', order: 'desc' })
  const isLoading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const totalProducts = ref(0)
  const searchQuery = ref('')
  const lastError = ref<string | null>(null)

  // === 计算属性 ===
  const filteredProducts = computed(() => {
    let result = [...products.value]

    // 应用搜索
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(product => 
        product.name?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
      )
    }

    // 应用分类筛选
    if (filters.value.category) {
      result = result.filter(product => 
        product.productCategories?.nodes?.some((cat: any) => 
          cat.slug === filters.value.category
        )
      )
    }

    // 应用价格筛选
    if (filters.value.priceRange) {
      const [min, max] = filters.value.priceRange
      result = result.filter(product => {
        const price = parseFloat(product.price?.replace(/[^\d.]/g, '') || '0')
        return price >= min && price <= max
      })
    }

    // 应用促销筛选
    if (filters.value.onSale) {
      result = result.filter(product => product.onSale)
    }

    // 应用库存筛选
    if (filters.value.inStock) {
      result = result.filter(product => product.stockStatus === 'IN_STOCK')
    }

    return result
  })

  const sortedProducts = computed(() => {
    const result = [...filteredProducts.value]
    
    return result.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sort.value.field) {
        case 'price':
          aValue = parseFloat(a.price?.replace(/[^\d.]/g, '') || '0')
          bValue = parseFloat(b.price?.replace(/[^\d.]/g, '') || '0')
          break
        case 'title':
          aValue = a.name || ''
          bValue = b.name || ''
          break
        case 'date':
          aValue = new Date(a.date || 0)
          bValue = new Date(b.date || 0)
          break
        case 'popularity':
          aValue = a.totalSales || 0
          bValue = b.totalSales || 0
          break
        case 'rating':
          aValue = a.averageRating || 0
          bValue = b.averageRating || 0
          break
        default:
          return 0
      }

      if (sort.value.order === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  })

  const productsByCategory = computed(() => {
    const grouped: Record<string, any[]> = {}
    
    products.value.forEach(product => {
      product.productCategories?.nodes?.forEach((category: any) => {
        if (!grouped[category.slug]) {
          grouped[category.slug] = []
        }
        grouped[category.slug].push(product)
      })
    })
    
    return grouped
  })

  // === 内部辅助函数 ===
  const setError = (error: string | null) => {
    lastError.value = error
  }

  const handleAsyncAction = async <T>(
    action: () => Promise<T>,
    errorMessage: string = '操作失败'
  ): Promise<T | null> => {
    isLoading.value = true
    setError(null)
    
    try {
      const result = await action()
      return result
    } catch (error) {
      console.error(errorMessage, error)
      setError(error instanceof Error ? error.message : errorMessage)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // === 基础动作 ===
  const setProducts = (newProducts: any[]) => {
    products.value = newProducts
  }

  const addProducts = (newProducts: any[]) => {
    products.value.push(...newProducts)
  }

  const setCategories = (newCategories: any[]) => {
    categories.value = newCategories
  }

  const setCurrentProduct = (product: any) => {
    currentProduct.value = product
  }

  const updateFilters = (newFilters: Partial<ProductFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
    currentPage.value = 1 // 重置页码
  }

  const clearFilters = () => {
    filters.value = {}
    searchQuery.value = ''
    currentPage.value = 1
  }

  const setSort = (newSort: Partial<ProductSort>) => {
    sort.value = { ...sort.value, ...newSort }
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
    currentPage.value = 1
  }

  // === 高级操作 ===
  const fetchProducts = async (options: {
    reset?: boolean
    loadMore?: boolean
    filters?: ProductFilters
  } = {}) => {
    return handleAsyncAction(async () => {
      const { reset = false, loadMore = false, filters: customFilters } = options
      
      if (reset) {
        currentPage.value = 1
        products.value = []
        hasMore.value = true
      }

      const page = loadMore ? currentPage.value + 1 : currentPage.value
      const activeFilters = customFilters || filters.value

      // 这里应该调用实际的API
      // 暂时使用模拟数据
      const mockProducts = Array.from({ length: 12 }, (_, i) => ({
        id: `product-${page}-${i}`,
        name: `Product ${page}-${i}`,
        price: `$${(Math.random() * 100 + 10).toFixed(2)}`,
        onSale: Math.random() > 0.7,
        stockStatus: Math.random() > 0.1 ? 'IN_STOCK' : 'OUT_OF_STOCK',
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        averageRating: Math.random() * 5,
        totalSales: Math.floor(Math.random() * 1000)
      }))

      if (loadMore) {
        addProducts(mockProducts)
        currentPage.value = page
      } else {
        setProducts(mockProducts)
      }

      hasMore.value = page < 5 // 模拟最多5页
      totalProducts.value = 60 // 模拟总数

      return mockProducts
    }, '获取产品失败')
  }

  const fetchProductBySlug = async (slug: string) => {
    return handleAsyncAction(async () => {
      // 这里应该调用实际的API
      const mockProduct = {
        id: `product-${slug}`,
        slug,
        name: `Product ${slug}`,
        price: `$${(Math.random() * 100 + 10).toFixed(2)}`,
        description: `Description for ${slug}`,
        onSale: Math.random() > 0.7,
        stockStatus: 'IN_STOCK'
      }

      setCurrentProduct(mockProduct)
      return mockProduct
    }, '获取产品详情失败')
  }

  const fetchCategories = async () => {
    return handleAsyncAction(async () => {
      // 这里应该调用实际的API
      const mockCategories = [
        { id: '1', name: '狗粮', slug: 'dog-food' },
        { id: '2', name: '猫粮', slug: 'cat-food' },
        { id: '3', name: '玩具', slug: 'toys' },
        { id: '4', name: '用品', slug: 'supplies' }
      ]

      setCategories(mockCategories)
      return mockCategories
    }, '获取分类失败')
  }

  const searchProducts = async (query: string) => {
    setSearchQuery(query)
    return fetchProducts({ reset: true })
  }

  // === 工具方法 ===
  const getProductById = (id: string) => {
    return products.value.find(product => product.id === id)
  }

  const getProductsByCategory = (categorySlug: string) => {
    return productsByCategory.value[categorySlug] || []
  }

  const resetStore = () => {
    products.value = []
    categories.value = []
    currentProduct.value = null
    filters.value = {}
    sort.value = { field: 'date', order: 'desc' }
    isLoading.value = false
    hasMore.value = true
    currentPage.value = 1
    totalProducts.value = 0
    searchQuery.value = ''
    lastError.value = null
  }

  return {
    // === 状态 ===
    products: readonly(products),
    categories: readonly(categories),
    currentProduct: readonly(currentProduct),
    filters: readonly(filters),
    sort: readonly(sort),
    isLoading: readonly(isLoading),
    hasMore: readonly(hasMore),
    currentPage: readonly(currentPage),
    totalProducts: readonly(totalProducts),
    searchQuery: readonly(searchQuery),
    lastError: readonly(lastError),

    // === 计算属性 ===
    filteredProducts,
    sortedProducts,
    productsByCategory,

    // === 动作 ===
    setProducts,
    addProducts,
    setCategories,
    setCurrentProduct,
    updateFilters,
    clearFilters,
    setSort,
    setSearchQuery,
    fetchProducts,
    fetchProductBySlug,
    fetchCategories,
    searchProducts,
    getProductById,
    getProductsByCategory,
    resetStore
  }
})
