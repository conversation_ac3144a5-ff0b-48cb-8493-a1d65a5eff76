// Pinia Stores 统一导出
export { useCartStore } from './cart'
export { useAuthStore } from './auth'
export { useUIStore, useNotifications } from './ui'

// 便捷的组合hooks
export const useStores = () => ({
  cart: useCartStore(),
  auth: useAuthStore(),
  ui: useUIStore()
})

// 初始化所有stores的方法
export const initializeStores = async () => {
  try {
    // 动态导入避免循环依赖
    const { useCartStore } = await import('./cart')
    const { useAuthStore } = await import('./auth')

    const cartStore = useCartStore()
    const authStore = useAuthStore()

    // 刷新购物车
    await cartStore.refreshCart()

    // 刷新用户数据
    if (authStore.viewer) {
      await authStore.refreshUserData()
    }

    console.log('✅ Pinia stores initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize stores:', error)
  }
}

// 清理所有stores的方法
export const clearAllStores = async () => {
  try {
    // 动态导入避免循环依赖
    const { useCartStore } = await import('./cart')
    const { useAuthStore } = await import('./auth')
    const { useUIStore } = await import('./ui')

    const cartStore = useCartStore()
    const authStore = useAuthStore()
    const uiStore = useUIStore()

    cartStore.setCart(null)
    authStore.clearAuth()
    uiStore.closeAllOverlays()

    console.log('🧹 All stores cleared')
  } catch (error) {
    console.error('❌ Failed to clear stores:', error)
  }
}
