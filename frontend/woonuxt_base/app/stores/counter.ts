import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', () => {
  // 状态
  const count = ref(0)

  // 计算属性
  const doubleCount = computed(() => count.value * 2)
  const isEven = computed(() => count.value % 2 === 0)

  // 动作
  const increment = () => {
    count.value++
  }

  const decrement = () => {
    count.value--
  }

  const reset = () => {
    count.value = 0
  }

  const incrementBy = (amount: number) => {
    count.value += amount
  }

  return {
    // 状态
    count: readonly(count),
    
    // 计算属性
    doubleCount,
    isEven,
    
    // 动作
    increment,
    decrement,
    reset,
    incrementBy
  }
})
