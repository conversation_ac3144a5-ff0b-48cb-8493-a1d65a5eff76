import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const customer = ref(null)
  const viewer = ref(null)
  const orders = ref(null)
  const downloads = ref(null)
  const loginClients = ref(null)
  const isPending = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!viewer.value
  })

  const avatar = computed(() => {
    return viewer.value?.avatar?.url || null
  })

  const wishlistLink = computed(() => {
    // 在服务端渲染时始终返回 /wishlist 以避免水合不匹配
    if (import.meta.server) {
      return '/wishlist'
    }
    // 在客户端根据用户状态返回相应链接
    return viewer.value ? '/my-account?tab=wishlist' : '/wishlist'
  })

  // 动作
  const setCustomer = (newCustomer: any) => {
    customer.value = newCustomer
  }

  const setViewer = (newViewer: any) => {
    viewer.value = newViewer
  }

  const setOrders = (newOrders: any) => {
    orders.value = newOrders
  }

  const setDownloads = (newDownloads: any) => {
    downloads.value = newDownloads
  }

  const setLoginClients = (clients: any) => {
    loginClients.value = clients
  }

  const setPending = (pending: boolean) => {
    isPending.value = pending
  }

  const loginUser = async (credentials: { username: string; password: string }) => {
    isPending.value = true
    try {
      const { login } = useAuth()
      const result = await login(credentials.username, credentials.password)
      
      if (result.success) {
        // 刷新用户数据
        await refreshUserData()
        
        // 刷新购物车
        const cartStore = useCartStore()
        await cartStore.refreshCart()
        
        return { success: true }
      } else {
        return { success: false, error: result.error || '登录失败' }
      }
    } catch (error: any) {
      console.error('登录错误:', error)
      return { 
        success: false, 
        error: error.message || '登录失败，请重试' 
      }
    } finally {
      isPending.value = false
    }
  }

  const logoutUser = async () => {
    isPending.value = true
    try {
      const { logout } = useAuth()
      const result = await logout()
      
      if (result.success) {
        // 清除所有认证数据
        customer.value = null
        viewer.value = null
        orders.value = null
        downloads.value = null
        loginClients.value = null
        
        // 刷新购物车
        const cartStore = useCartStore()
        await cartStore.refreshCart()
        
        return { success: true }
      } else {
        return { success: false, error: '登出失败' }
      }
    } catch (error: any) {
      console.error('登出错误:', error)
      return { 
        success: false, 
        error: error.message || '登出失败，请重试' 
      }
    } finally {
      isPending.value = false
    }
  }

  const registerUser = async (userInfo: any) => {
    isPending.value = true
    try {
      const { register } = useAuth()
      await register(userInfo)
      return { success: true }
    } catch (error: any) {
      console.error('注册错误:', error)
      return { 
        success: false, 
        error: error.message || '注册失败，请重试' 
      }
    } finally {
      isPending.value = false
    }
  }

  const refreshUserData = async () => {
    isPending.value = true
    try {
      const { viewer: viewerData } = useAuth()
      viewer.value = viewerData.value
    } catch (error) {
      console.error('刷新用户数据失败:', error)
    } finally {
      isPending.value = false
    }
  }

  const clearAuth = () => {
    customer.value = null
    viewer.value = null
    orders.value = null
    downloads.value = null
    loginClients.value = null
    isPending.value = false
  }

  return {
    // 状态
    customer: readonly(customer),
    viewer: readonly(viewer),
    orders: readonly(orders),
    downloads: readonly(downloads),
    loginClients: readonly(loginClients),
    isPending: readonly(isPending),
    
    // 计算属性
    isLoggedIn,
    avatar,
    wishlistLink,
    
    // 动作
    setCustomer,
    setViewer,
    setOrders,
    setDownloads,
    setLoginClients,
    setPending,
    loginUser,
    logoutUser,
    registerUser,
    refreshUserData,
    clearAuth
  }
})
