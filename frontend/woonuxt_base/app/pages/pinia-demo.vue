<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Pinia 状态管理演示</h1>

    <!-- 简单计数器演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">简单计数器</h2>
      <SimpleCounter />
    </section>

    <!-- 高级购物车演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">高级购物车 (Composition API)</h2>
      <AdvancedCartDemo />
    </section>

    <!-- 响应式系统演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">响应式系统 (Reactivity System)</h2>
      <ReactivityDemo />
    </section>

    <!-- 购物车状态演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">购物车状态</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">{{ itemCount }}</div>
            <div class="text-gray-600">商品数量</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ subtotal }}</div>
            <div class="text-gray-600">小计</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold" :class="isCartLoading ? 'text-yellow-600' : 'text-gray-600'">
              {{ isCartLoading ? '加载中' : '就绪' }}
            </div>
            <div class="text-gray-600">状态</div>
          </div>
        </div>
        
        <div class="flex gap-4">
          <button
            @click="toggleCart"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {{ isShowingCart ? '关闭' : '打开' }}购物车
          </button>
          
          <button
            @click="refreshCart"
            :disabled="isCartLoading"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            刷新购物车
          </button>
          
          <button
            @click="clearCart"
            :disabled="isCartLoading"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            清空购物车
          </button>
        </div>
      </div>
    </section>

    <!-- 用户认证状态演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">用户认证状态</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="mb-4">
          <div class="text-lg">
            状态: 
            <span :class="isLoggedIn ? 'text-green-600' : 'text-red-600'">
              {{ isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>
          <div v-if="viewer" class="text-gray-600">
            用户: {{ viewer.username || viewer.email }}
          </div>
          <div class="text-gray-600">
            心愿单链接: {{ wishlistLink }}
          </div>
        </div>
        
        <div class="flex gap-4">
          <button
            v-if="!isLoggedIn"
            @click="showLoginForm = !showLoginForm"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {{ showLoginForm ? '隐藏' : '显示' }}登录表单
          </button>
          
          <button
            v-if="isLoggedIn"
            @click="logout"
            :disabled="isPending"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            登出
          </button>
          
          <button
            @click="refreshUserData"
            :disabled="isPending"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            刷新用户数据
          </button>
        </div>
        
        <!-- 简单登录表单 -->
        <div v-if="showLoginForm && !isLoggedIn" class="mt-6 p-4 bg-gray-50 rounded">
          <h3 class="text-lg font-semibold mb-4">登录</h3>
          <div class="space-y-4">
            <input
              v-model="loginForm.username"
              type="text"
              placeholder="用户名或邮箱"
              class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              @click="login"
              :disabled="isPending"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {{ isPending ? '登录中...' : '登录' }}
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- UI 状态演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">UI 状态管理</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div class="text-center">
            <div class="text-lg font-semibold" :class="isShowingMobileMenu ? 'text-green-600' : 'text-gray-600'">
              {{ isShowingMobileMenu ? '开启' : '关闭' }}
            </div>
            <div class="text-gray-600">移动菜单</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold" :class="isShowingSearch ? 'text-green-600' : 'text-gray-600'">
              {{ isShowingSearch ? '开启' : '关闭' }}
            </div>
            <div class="text-gray-600">搜索</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold" :class="isShowingFilters ? 'text-green-600' : 'text-gray-600'">
              {{ isShowingFilters ? '开启' : '关闭' }}
            </div>
            <div class="text-gray-600">筛选器</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-blue-600">{{ theme }}</div>
            <div class="text-gray-600">主题</div>
          </div>
        </div>
        
        <div class="flex flex-wrap gap-4">
          <button
            @click="toggleMobileMenu"
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            切换移动菜单
          </button>
          
          <button
            @click="toggleSearch"
            class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
          >
            切换搜索
          </button>
          
          <button
            @click="toggleFilters"
            class="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600"
          >
            切换筛选器
          </button>
          
          <button
            @click="cycleTheme"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            切换主题
          </button>
          
          <button
            @click="showSuccessNotification"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            成功通知
          </button>
          
          <button
            @click="showErrorNotification"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            错误通知
          </button>
        </div>
      </div>
    </section>

    <!-- 通知容器 -->
    <NotificationContainer />
  </div>
</template>

<script setup lang="ts">
import { useCartStore, useAuthStore, useUIStore, useNotifications } from '~/stores'
import NotificationContainer from '~/components/pinia/NotificationContainer.vue'
import SimpleCounter from '~/components/pinia/SimpleCounter.vue'
import AdvancedCartDemo from '~/components/pinia/AdvancedCartDemo.vue'
import ReactivityDemo from '~/components/pinia/ReactivityDemo.vue'

// 页面元数据
definePageMeta({
  title: 'Pinia 演示'
})

// 使用 Pinia stores
const cartStore = useCartStore()
const authStore = useAuthStore()
const uiStore = useUIStore()
const notifications = useNotifications()

// 购物车状态
const itemCount = computed(() => cartStore.itemCount)
const subtotal = computed(() => cartStore.subtotal)
const isCartLoading = computed(() => cartStore.isLoading)
const isShowingCart = computed(() => cartStore.isShowingCart)

// 认证状态
const isLoggedIn = computed(() => authStore.isLoggedIn)
const viewer = computed(() => authStore.viewer)
const wishlistLink = computed(() => authStore.wishlistLink)
const isPending = computed(() => authStore.isPending)

// UI 状态
const isShowingMobileMenu = computed(() => uiStore.isShowingMobileMenu)
const isShowingSearch = computed(() => uiStore.isShowingSearch)
const isShowingFilters = computed(() => uiStore.isShowingFilters)
const theme = computed(() => uiStore.theme)

// 本地状态
const showLoginForm = ref(false)
const loginForm = reactive({
  username: '',
  password: ''
})

// 购物车动作
const toggleCart = () => cartStore.toggleCart()
const refreshCart = () => cartStore.refreshCart()
const clearCart = () => cartStore.clearCart()

// 认证动作
const login = async () => {
  const result = await authStore.loginUser(loginForm)
  if (result.success) {
    notifications.success('登录成功', '欢迎回来！')
    showLoginForm.value = false
    loginForm.username = ''
    loginForm.password = ''
  } else {
    notifications.error('登录失败', result.error)
  }
}

const logout = async () => {
  const result = await authStore.logoutUser()
  if (result.success) {
    notifications.success('登出成功', '再见！')
  } else {
    notifications.error('登出失败', result.error)
  }
}

const refreshUserData = () => authStore.refreshUserData()

// UI 动作
const toggleMobileMenu = () => uiStore.toggleMobileMenu()
const toggleSearch = () => uiStore.toggleSearch()
const toggleFilters = () => uiStore.toggleFilters()

const cycleTheme = () => {
  const themes = ['light', 'dark', 'auto'] as const
  const currentIndex = themes.indexOf(theme.value)
  const nextTheme = themes[(currentIndex + 1) % themes.length]
  uiStore.setTheme(nextTheme)
}

const showSuccessNotification = () => {
  notifications.success('操作成功', '这是一个成功通知的示例')
}

const showErrorNotification = () => {
  notifications.error('操作失败', '这是一个错误通知的示例')
}
</script>
