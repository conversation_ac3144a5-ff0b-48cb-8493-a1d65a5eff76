<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Pinia + Composition API 简化演示</h1>
    
    <!-- 简单计数器演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">简单计数器</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="text-center mb-6">
          <div class="text-4xl font-bold text-blue-600 mb-2">{{ count }}</div>
          <div class="text-gray-600">当前计数</div>
        </div>
        
        <div class="flex gap-4 justify-center">
          <button
            @click="increment"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            +1
          </button>
          
          <button
            @click="decrement"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            -1
          </button>
          
          <button
            @click="reset"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            重置
          </button>
        </div>
        
        <div class="mt-4 text-sm text-gray-600 text-center">
          双倍值: {{ doubleCount }} | 是否为偶数: {{ isEven ? '是' : '否' }}
        </div>
      </div>
    </section>

    <!-- 购物车基础演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">购物车状态</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">{{ cartItemCount }}</div>
            <div class="text-gray-600">商品数量</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ cartSubtotal }}</div>
            <div class="text-gray-600">小计</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold" :class="cartIsLoading ? 'text-yellow-600' : 'text-gray-600'">
              {{ cartIsLoading ? '加载中' : '就绪' }}
            </div>
            <div class="text-gray-600">状态</div>
          </div>
        </div>
        
        <div class="flex gap-4">
          <button
            @click="toggleCartVisibility"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {{ cartIsShowing ? '关闭' : '打开' }}购物车
          </button>
          
          <button
            @click="refreshCartData"
            :disabled="cartIsLoading"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            刷新购物车
          </button>
          
          <button
            @click="clearCartData"
            :disabled="cartIsLoading"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            清空购物车
          </button>
        </div>
      </div>
    </section>

    <!-- UI 状态演示 -->
    <section class="mb-12">
      <h2 class="text-2xl font-semibold mb-4">UI 状态管理</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div class="text-center">
            <div class="text-lg font-semibold" :class="uiMobileMenu ? 'text-green-600' : 'text-gray-600'">
              {{ uiMobileMenu ? '开启' : '关闭' }}
            </div>
            <div class="text-gray-600">移动菜单</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold" :class="uiSearch ? 'text-green-600' : 'text-gray-600'">
              {{ uiSearch ? '开启' : '关闭' }}
            </div>
            <div class="text-gray-600">搜索</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold" :class="uiFilters ? 'text-green-600' : 'text-gray-600'">
              {{ uiFilters ? '开启' : '关闭' }}
            </div>
            <div class="text-gray-600">筛选器</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-blue-600">{{ uiTheme }}</div>
            <div class="text-gray-600">主题</div>
          </div>
        </div>
        
        <div class="flex flex-wrap gap-4">
          <button
            @click="toggleMobileMenuState"
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            切换移动菜单
          </button>
          
          <button
            @click="toggleSearchState"
            class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
          >
            切换搜索
          </button>
          
          <button
            @click="toggleFiltersState"
            class="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600"
          >
            切换筛选器
          </button>
          
          <button
            @click="cycleThemeState"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            切换主题
          </button>
          
          <button
            @click="showSuccessMessage"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            成功通知
          </button>
          
          <button
            @click="showErrorMessage"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            错误通知
          </button>
        </div>
      </div>
    </section>

    <!-- 通知容器 -->
    <ClientOnly>
      <NotificationContainer />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import NotificationContainer from '~/components/pinia/NotificationContainer.vue'

// 页面元数据
definePageMeta({
  title: 'Pinia 简化演示'
})

// === 计数器Store ===
const counterStore = useCounterStore()

// 计数器状态
const count = computed(() => counterStore.count)
const doubleCount = computed(() => counterStore.doubleCount)
const isEven = computed(() => counterStore.isEven)

// 计数器动作
const increment = () => counterStore.increment()
const decrement = () => counterStore.decrement()
const reset = () => counterStore.reset()

// === 购物车Store ===
const cartStore = useCartStore()

// 购物车状态
const cartItemCount = computed(() => cartStore.itemCount || 0)
const cartSubtotal = computed(() => cartStore.subtotal || '0')
const cartIsLoading = computed(() => cartStore.isLoading || false)
const cartIsShowing = computed(() => cartStore.isShowingCart || false)

// 购物车动作
const toggleCartVisibility = () => cartStore.toggleCart()
const refreshCartData = () => cartStore.refreshCart()
const clearCartData = () => cartStore.clearCart()

// === UI Store ===
const uiStore = useUIStore()

// UI状态
const uiMobileMenu = computed(() => uiStore.isShowingMobileMenu || false)
const uiSearch = computed(() => uiStore.isShowingSearch || false)
const uiFilters = computed(() => uiStore.isShowingFilters || false)
const uiTheme = computed(() => uiStore.theme || 'auto')

// UI动作
const toggleMobileMenuState = () => uiStore.toggleMobileMenu()
const toggleSearchState = () => uiStore.toggleSearch()
const toggleFiltersState = () => uiStore.toggleFilters()

const cycleThemeState = () => {
  const themes = ['light', 'dark', 'auto'] as const
  const currentIndex = themes.indexOf(uiTheme.value)
  const nextTheme = themes[(currentIndex + 1) % themes.length]
  uiStore.setTheme(nextTheme)
}

// 通知功能
const { useNotifications } = await import('~/stores/ui')
const notifications = useNotifications()

const showSuccessMessage = () => {
  notifications.success('操作成功', '这是一个成功通知的示例')
}

const showErrorMessage = () => {
  notifications.error('操作失败', '这是一个错误通知的示例')
}

// 页面初始化
onMounted(() => {
  console.log('✅ Pinia 简化演示页面已加载')
})
</script>
