export default defineNuxtPlugin(async () => {
  // 只在客户端运行
  if (import.meta.server) return

  console.log('🚀 Initializing Pinia stores with Composition API...')

  try {
    // 动态导入stores以避免SSR问题
    const { initializeStores } = await import('~/stores')
    const { useUIStore } = await import('~/stores/ui')

    // 初始化主题
    const uiStore = useUIStore()
    uiStore.setTheme(uiStore.theme)

    // 监听系统主题变化
    if (uiStore.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleThemeChange = (e: MediaQueryListEvent) => {
        const root = document.documentElement
        if (e.matches) {
          root.classList.add('dark')
        } else {
          root.classList.remove('dark')
        }
      }

      mediaQuery.addEventListener('change', handleThemeChange)

      // 在页面卸载时清理
      window.addEventListener('beforeunload', () => {
        mediaQuery.removeEventListener('change', handleThemeChange)
      })
    }

    // 初始化所有stores
    await initializeStores()

    // 监听路由变化，关闭覆盖层
    const router = useRouter()
    router.afterEach(() => {
      uiStore.closeAllOverlays()
    })

    // 添加全局错误处理
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection in Pinia store:', event.reason)
      uiStore.addNotification({
        type: 'error',
        title: '系统错误',
        message: '发生了未处理的错误，请刷新页面重试'
      })
    })

    console.log('✅ Pinia stores with Composition API initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize Pinia stores:', error)
  }
})
