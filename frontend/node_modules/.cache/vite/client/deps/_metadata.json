{"hash": "c833bf0a", "configHash": "c6c03623", "lockfileHash": "0e5861b9", "browserHash": "5fd90e73", "optimized": {"nuxt-graphql-client > graphql-request": {"src": "../../../../graphql-request/build/entrypoints/main.js", "file": "nuxt-graphql-client___graphql-request.js", "fileHash": "c625e942", "needsInterop": false}, "@vue/devtools-core": {"src": "../../../../@vue/devtools-core/dist/index.js", "file": "@vue_devtools-core.js", "fileHash": "e7cf2606", "needsInterop": false}, "@vue/devtools-kit": {"src": "../../../../@vue/devtools-kit/dist/index.js", "file": "@vue_devtools-kit.js", "fileHash": "fb419f55", "needsInterop": false}, "@intlify/shared": {"src": "../../../../@intlify/shared/dist/shared.mjs", "file": "@intlify_shared.js", "fileHash": "c4051efd", "needsInterop": false}, "@intlify/core-base": {"src": "../../../../@intlify/core-base/dist/core-base.mjs", "file": "@intlify_core-base.js", "fileHash": "f8a2f047", "needsInterop": false}, "@vueform/slider": {"src": "../../../../@vueform/slider/dist/slider.js", "file": "@vueform_slider.js", "fileHash": "f28492d2", "needsInterop": false}}, "chunks": {"chunk-OT3Q6CPD": {"file": "chunk-OT3Q6CPD.js"}, "chunk-APDJLZXF": {"file": "chunk-APDJLZXF.js"}}}