import { D as Driver, S as StorageValue, a as Storage } from './shared/unstorage.Ca7R4QL2.cjs';
export { d as DriverFlags, G as GetKeysOptions, c as StorageMeta, T as TransactionOptions, U as Unwatch, b as WatchCallback, W as WatchEvent } from './shared/unstorage.Ca7R4QL2.cjs';
import { AzureAppConfigurationOptions } from 'unstorage/drivers/azure-app-configuration';
import { AzureCosmosOptions } from 'unstorage/drivers/azure-cosmos';
import { AzureKeyVaultOptions } from 'unstorage/drivers/azure-key-vault';
import { AzureStorageBlobOptions } from 'unstorage/drivers/azure-storage-blob';
import { AzureStorageTableOptions } from 'unstorage/drivers/azure-storage-table';
import { CapacitorPreferencesOptions } from 'unstorage/drivers/capacitor-preferences';
import { KVOptions } from 'unstorage/drivers/cloudflare-kv-binding';
import { KVHTTPOptions } from 'unstorage/drivers/cloudflare-kv-http';
import { CloudflareR2Options } from 'unstorage/drivers/cloudflare-r2-binding';
import { DB0DriverOptions } from 'unstorage/drivers/db0';
import { DenoKvNodeOptions } from 'unstorage/drivers/deno-kv-node';
import { DenoKvOptions } from 'unstorage/drivers/deno-kv';
import { FSStorageOptions } from 'unstorage/drivers/fs-lite';
import { FSStorageOptions as FSStorageOptions$1 } from 'unstorage/drivers/fs';
import { GithubOptions } from 'unstorage/drivers/github';
import { HTTPOptions } from 'unstorage/drivers/http';
import { IDBKeyvalOptions } from 'unstorage/drivers/indexedb';
import { LocalStorageOptions } from 'unstorage/drivers/localstorage';
import { LRUDriverOptions } from 'unstorage/drivers/lru-cache';
import { MongoDbOptions } from 'unstorage/drivers/mongodb';
import { NetlifyStoreOptions } from 'unstorage/drivers/netlify-blobs';
import { OverlayStorageOptions } from 'unstorage/drivers/overlay';
import { PlanetscaleDriverOptions } from 'unstorage/drivers/planetscale';
import { RedisOptions } from 'unstorage/drivers/redis';
import { S3DriverOptions } from 'unstorage/drivers/s3';
import { SessionStorageOptions } from 'unstorage/drivers/session-storage';
import { UploadThingOptions } from 'unstorage/drivers/uploadthing';
import { UpstashOptions } from 'unstorage/drivers/upstash';
import { VercelBlobOptions } from 'unstorage/drivers/vercel-blob';
import { VercelKVOptions } from 'unstorage/drivers/vercel-kv';

interface CreateStorageOptions {
    driver?: Driver;
}
declare function createStorage<T extends StorageValue>(options?: CreateStorageOptions): Storage<T>;
type Snapshot<T = string> = Record<string, T>;
declare function snapshot(storage: Storage, base: string): Promise<Snapshot<string>>;
declare function restoreSnapshot(driver: Storage, snapshot: Snapshot<StorageValue>, base?: string): Promise<void>;

declare function prefixStorage<T extends StorageValue>(storage: Storage<T> | Storage<any>, base: string): Storage<T>;
declare function normalizeKey(key?: string): string;
declare function joinKeys(...keys: string[]): string;
declare function normalizeBaseKey(base?: string): string;
declare function filterKeyByDepth(key: string, depth: number | undefined): boolean;
declare function filterKeyByBase(key: string, base: string | undefined): boolean;

type DriverFactory<OptionsT, InstanceT> = (opts: OptionsT) => Driver<OptionsT, InstanceT>;
declare function defineDriver<OptionsT = any, InstanceT = never>(factory: DriverFactory<OptionsT, InstanceT>): DriverFactory<OptionsT, InstanceT>;

type BuiltinDriverName = "azure-app-configuration" | "azureAppConfiguration" | "azure-cosmos" | "azureCosmos" | "azure-key-vault" | "azureKeyVault" | "azure-storage-blob" | "azureStorageBlob" | "azure-storage-table" | "azureStorageTable" | "capacitor-preferences" | "capacitorPreferences" | "cloudflare-kv-binding" | "cloudflareKVBinding" | "cloudflare-kv-http" | "cloudflareKVHttp" | "cloudflare-r2-binding" | "cloudflareR2Binding" | "db0" | "deno-kv-node" | "denoKVNode" | "deno-kv" | "denoKV" | "fs-lite" | "fsLite" | "fs" | "github" | "http" | "indexedb" | "localstorage" | "lru-cache" | "lruCache" | "memory" | "mongodb" | "netlify-blobs" | "netlifyBlobs" | "null" | "overlay" | "planetscale" | "redis" | "s3" | "session-storage" | "sessionStorage" | "uploadthing" | "upstash" | "vercel-blob" | "vercelBlob" | "vercel-kv" | "vercelKV";
type BuiltinDriverOptions = {
    "azure-app-configuration": AzureAppConfigurationOptions;
    "azureAppConfiguration": AzureAppConfigurationOptions;
    "azure-cosmos": AzureCosmosOptions;
    "azureCosmos": AzureCosmosOptions;
    "azure-key-vault": AzureKeyVaultOptions;
    "azureKeyVault": AzureKeyVaultOptions;
    "azure-storage-blob": AzureStorageBlobOptions;
    "azureStorageBlob": AzureStorageBlobOptions;
    "azure-storage-table": AzureStorageTableOptions;
    "azureStorageTable": AzureStorageTableOptions;
    "capacitor-preferences": CapacitorPreferencesOptions;
    "capacitorPreferences": CapacitorPreferencesOptions;
    "cloudflare-kv-binding": KVOptions;
    "cloudflareKVBinding": KVOptions;
    "cloudflare-kv-http": KVHTTPOptions;
    "cloudflareKVHttp": KVHTTPOptions;
    "cloudflare-r2-binding": CloudflareR2Options;
    "cloudflareR2Binding": CloudflareR2Options;
    "db0": DB0DriverOptions;
    "deno-kv-node": DenoKvNodeOptions;
    "denoKVNode": DenoKvNodeOptions;
    "deno-kv": DenoKvOptions;
    "denoKV": DenoKvOptions;
    "fs-lite": FSStorageOptions;
    "fsLite": FSStorageOptions;
    "fs": FSStorageOptions$1;
    "github": GithubOptions;
    "http": HTTPOptions;
    "indexedb": IDBKeyvalOptions;
    "localstorage": LocalStorageOptions;
    "lru-cache": LRUDriverOptions;
    "lruCache": LRUDriverOptions;
    "mongodb": MongoDbOptions;
    "netlify-blobs": NetlifyStoreOptions;
    "netlifyBlobs": NetlifyStoreOptions;
    "overlay": OverlayStorageOptions;
    "planetscale": PlanetscaleDriverOptions;
    "redis": RedisOptions;
    "s3": S3DriverOptions;
    "session-storage": SessionStorageOptions;
    "sessionStorage": SessionStorageOptions;
    "uploadthing": UploadThingOptions;
    "upstash": UpstashOptions;
    "vercel-blob": VercelBlobOptions;
    "vercelBlob": VercelBlobOptions;
    "vercel-kv": VercelKVOptions;
    "vercelKV": VercelKVOptions;
};
declare const builtinDrivers: {
    readonly "azure-app-configuration": "unstorage/drivers/azure-app-configuration";
    readonly azureAppConfiguration: "unstorage/drivers/azure-app-configuration";
    readonly "azure-cosmos": "unstorage/drivers/azure-cosmos";
    readonly azureCosmos: "unstorage/drivers/azure-cosmos";
    readonly "azure-key-vault": "unstorage/drivers/azure-key-vault";
    readonly azureKeyVault: "unstorage/drivers/azure-key-vault";
    readonly "azure-storage-blob": "unstorage/drivers/azure-storage-blob";
    readonly azureStorageBlob: "unstorage/drivers/azure-storage-blob";
    readonly "azure-storage-table": "unstorage/drivers/azure-storage-table";
    readonly azureStorageTable: "unstorage/drivers/azure-storage-table";
    readonly "capacitor-preferences": "unstorage/drivers/capacitor-preferences";
    readonly capacitorPreferences: "unstorage/drivers/capacitor-preferences";
    readonly "cloudflare-kv-binding": "unstorage/drivers/cloudflare-kv-binding";
    readonly cloudflareKVBinding: "unstorage/drivers/cloudflare-kv-binding";
    readonly "cloudflare-kv-http": "unstorage/drivers/cloudflare-kv-http";
    readonly cloudflareKVHttp: "unstorage/drivers/cloudflare-kv-http";
    readonly "cloudflare-r2-binding": "unstorage/drivers/cloudflare-r2-binding";
    readonly cloudflareR2Binding: "unstorage/drivers/cloudflare-r2-binding";
    readonly db0: "unstorage/drivers/db0";
    readonly "deno-kv-node": "unstorage/drivers/deno-kv-node";
    readonly denoKVNode: "unstorage/drivers/deno-kv-node";
    readonly "deno-kv": "unstorage/drivers/deno-kv";
    readonly denoKV: "unstorage/drivers/deno-kv";
    readonly "fs-lite": "unstorage/drivers/fs-lite";
    readonly fsLite: "unstorage/drivers/fs-lite";
    readonly fs: "unstorage/drivers/fs";
    readonly github: "unstorage/drivers/github";
    readonly http: "unstorage/drivers/http";
    readonly indexedb: "unstorage/drivers/indexedb";
    readonly localstorage: "unstorage/drivers/localstorage";
    readonly "lru-cache": "unstorage/drivers/lru-cache";
    readonly lruCache: "unstorage/drivers/lru-cache";
    readonly memory: "unstorage/drivers/memory";
    readonly mongodb: "unstorage/drivers/mongodb";
    readonly "netlify-blobs": "unstorage/drivers/netlify-blobs";
    readonly netlifyBlobs: "unstorage/drivers/netlify-blobs";
    readonly null: "unstorage/drivers/null";
    readonly overlay: "unstorage/drivers/overlay";
    readonly planetscale: "unstorage/drivers/planetscale";
    readonly redis: "unstorage/drivers/redis";
    readonly s3: "unstorage/drivers/s3";
    readonly "session-storage": "unstorage/drivers/session-storage";
    readonly sessionStorage: "unstorage/drivers/session-storage";
    readonly uploadthing: "unstorage/drivers/uploadthing";
    readonly upstash: "unstorage/drivers/upstash";
    readonly "vercel-blob": "unstorage/drivers/vercel-blob";
    readonly vercelBlob: "unstorage/drivers/vercel-blob";
    readonly "vercel-kv": "unstorage/drivers/vercel-kv";
    readonly vercelKV: "unstorage/drivers/vercel-kv";
};

export { Driver, Storage, StorageValue, builtinDrivers, createStorage, defineDriver, filterKeyByBase, filterKeyByDepth, joinKeys, normalizeBaseKey, normalizeKey, prefixStorage, restoreSnapshot, snapshot };
export type { BuiltinDriverName, BuiltinDriverOptions, CreateStorageOptions, Snapshot };
