{"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes unless disabled.", "version": "2.1.1", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> <<EMAIL>> (https://twitter.com/<PERSON><PERSON><PERSON>litz)", "<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)"], "repository": "jonschlinkert/normalize-path", "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "devDependencies": {"benchmarked": "^0.1.1", "gulp-format-md": "^0.1.11", "minimist": "^1.2.0", "mocha": "*"}, "keywords": ["backslash", "file", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"related": {"list": ["contains-path", "ends-with", "is-absolute", "is-relative", "parse-filepath", "path-ends-with", "path-segments", "rewrite-ext", "unixify"], "description": "Other useful libraries for working with paths in node.js:"}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}