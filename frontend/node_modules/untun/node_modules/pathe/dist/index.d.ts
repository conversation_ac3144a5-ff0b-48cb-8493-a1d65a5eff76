import path$1 from 'node:path';

declare const sep = "/";
declare const delimiter = ":";
declare const normalize: typeof path$1.normalize;
declare const join: typeof path$1.join;
declare const resolve: typeof path$1.resolve;
declare function normalizeString(path: string, allowAboveRoot: boolean): string;
declare const isAbsolute: typeof path$1.isAbsolute;
declare const toNamespacedPath: typeof path$1.toNamespacedPath;
declare const extname: typeof path$1.extname;
declare const relative: typeof path$1.relative;
declare const dirname: typeof path$1.dirname;
declare const format: typeof path$1.format;
declare const basename: typeof path$1.basename;
declare const parse: typeof path$1.parse;

declare const path_basename: typeof basename;
declare const path_delimiter: typeof delimiter;
declare const path_dirname: typeof dirname;
declare const path_extname: typeof extname;
declare const path_format: typeof format;
declare const path_isAbsolute: typeof isAbsolute;
declare const path_join: typeof join;
declare const path_normalize: typeof normalize;
declare const path_normalizeString: typeof normalizeString;
declare const path_parse: typeof parse;
declare const path_relative: typeof relative;
declare const path_resolve: typeof resolve;
declare const path_sep: typeof sep;
declare const path_toNamespacedPath: typeof toNamespacedPath;
declare namespace path {
  export { path_basename as basename, path_delimiter as delimiter, path_dirname as dirname, path_extname as extname, path_format as format, path_isAbsolute as isAbsolute, path_join as join, path_normalize as normalize, path_normalizeString as normalizeString, path_parse as parse, path_relative as relative, path_resolve as resolve, path_sep as sep, path_toNamespacedPath as toNamespacedPath };
}

export { basename, path as default, delimiter, dirname, extname, format, isAbsolute, join, normalize, normalizeString, parse, relative, resolve, sep, toNamespacedPath };
