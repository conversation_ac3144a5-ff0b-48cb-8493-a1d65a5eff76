<script setup lang="ts">
// 宠物产品页面示例
import { ProductsOrderByEnum } from '#woo';

// SEO 设置
useSeoMeta({
  title: '宠物用品 - 专业宠物商城',
  description: '为您的爱宠提供最优质的用品，包括狗狗用品、猫咪用品、鸟类和鱼类用品',
  ogTitle: '宠物用品商城',
  ogDescription: '专业的宠物用品电商平台',
});

// 获取产品数据
const { data: productsData } = await useAsyncGql('getProducts', {
  first: 12,
  orderby: ProductsOrderByEnum.POPULARITY
});

const products = computed(() => productsData.value?.products?.nodes || []);

// 获取宠物分类
const { data: categoriesData } = await useAsyncGql('getProductCategories', {
  first: 10,
  where: { slug: ['dog-supplies', 'cat-supplies', 'bird-supplies', 'fish-supplies'] }
});

const petCategories = computed(() => categoriesData.value?.productCategories?.nodes || []);

// 筛选状态
const selectedCategory = ref('');
const selectedPetType = ref('');
const priceRange = ref([0, 1000]);

// 筛选后的产品
const filteredProducts = computed(() => {
  let filtered = products.value;
  
  if (selectedCategory.value) {
    filtered = filtered.filter(product => 
      product.productCategories?.nodes.some(cat => cat.slug === selectedCategory.value)
    );
  }
  
  return filtered;
});

// 宠物类型配置
const petTypes = [
  { name: '狗狗', value: 'dog', icon: '🐕', color: 'text-amber-600' },
  { name: '猫咪', value: 'cat', icon: '🐱', color: 'text-gray-600' },
  { name: '鸟类', value: 'bird', icon: '🐦', color: 'text-blue-600' },
  { name: '鱼类', value: 'fish', icon: '🐠', color: 'text-cyan-600' },
];
</script>

<template>
  <div class="pet-products-page">
    <!-- 页面头部 -->
    <section class="bg-gradient-to-r from-orange-100 to-amber-100 py-12">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-gray-800 mb-4">
            🐾 宠物用品专区
          </h1>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            为您的爱宠精选优质用品，让每一只宠物都能享受最好的生活
          </p>
        </div>
      </div>
    </section>

    <!-- 宠物类型快速导航 -->
    <section class="py-8 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            v-for="petType in petTypes"
            :key="petType.value"
            @click="selectedPetType = petType.value"
            :class="[
              'p-6 rounded-xl border-2 transition-all duration-200 hover:shadow-lg',
              selectedPetType === petType.value 
                ? 'border-orange-500 bg-orange-50' 
                : 'border-gray-200 hover:border-orange-300'
            ]"
          >
            <div class="text-center">
              <div class="text-4xl mb-2">{{ petType.icon }}</div>
              <h3 :class="['font-semibold', petType.color]">{{ petType.name }}</h3>
            </div>
          </button>
        </div>
      </div>
    </section>

    <!-- 主要内容区域 -->
    <section class="py-8">
      <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- 侧边栏筛选 -->
          <aside class="lg:w-1/4">
            <div class="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <h3 class="text-lg font-semibold mb-4">筛选条件</h3>
              
              <!-- 分类筛选 -->
              <div class="mb-6">
                <h4 class="font-medium mb-3">产品分类</h4>
                <div class="space-y-2">
                  <label class="flex items-center space-x-2 cursor-pointer">
                    <input
                      v-model="selectedCategory"
                      type="radio"
                      value=""
                      class="text-orange-500"
                    />
                    <span>全部分类</span>
                  </label>
                  <label
                    v-for="category in petCategories"
                    :key="category.slug"
                    class="flex items-center space-x-2 cursor-pointer"
                  >
                    <input
                      v-model="selectedCategory"
                      type="radio"
                      :value="category.slug"
                      class="text-orange-500"
                    />
                    <span>{{ category.name }}</span>
                  </label>
                </div>
              </div>

              <!-- 价格范围 -->
              <div class="mb-6">
                <h4 class="font-medium mb-3">价格范围</h4>
                <div class="flex items-center space-x-2">
                  <input
                    v-model="priceRange[0]"
                    type="number"
                    placeholder="最低价"
                    class="w-20 p-2 border rounded text-sm"
                  />
                  <span>-</span>
                  <input
                    v-model="priceRange[1]"
                    type="number"
                    placeholder="最高价"
                    class="w-20 p-2 border rounded text-sm"
                  />
                </div>
              </div>

              <!-- 特色标签 -->
              <div class="mb-6">
                <h4 class="font-medium mb-3">特色标签</h4>
                <div class="flex flex-wrap gap-2">
                  <button class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200">
                    有机天然
                  </button>
                  <button class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200">
                    兽医推荐
                  </button>
                  <button class="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200">
                    新品上市
                  </button>
                </div>
              </div>
            </div>
          </aside>

          <!-- 产品列表 -->
          <main class="lg:w-3/4">
            <!-- 排序和视图选项 -->
            <div class="flex justify-between items-center mb-6">
              <p class="text-gray-600">
                找到 {{ filteredProducts.length }} 件商品
              </p>
              <div class="flex items-center space-x-4">
                <select class="p-2 border rounded-md">
                  <option>按人气排序</option>
                  <option>按价格从低到高</option>
                  <option>按价格从高到低</option>
                  <option>按最新上架</option>
                </select>
              </div>
            </div>

            <!-- 产品网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="product in filteredProducts"
                :key="product.id"
                class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden"
              >
                <NuxtLink :to="`/product/${product.slug}`">
                  <!-- 产品图片 -->
                  <div class="aspect-square overflow-hidden">
                    <NuxtImg
                      :src="product.image?.sourceUrl || '/images/placeholder.jpg'"
                      :alt="product.name"
                      class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                      width="300"
                      height="300"
                    />
                  </div>
                  
                  <!-- 产品信息 -->
                  <div class="p-4">
                    <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2">
                      {{ product.name }}
                    </h3>
                    
                    <!-- 分类标签 -->
                    <div class="flex flex-wrap gap-1 mb-2">
                      <span
                        v-for="category in product.productCategories?.nodes.slice(0, 2)"
                        :key="category.id"
                        class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                      >
                        {{ category.name }}
                      </span>
                    </div>
                    
                    <!-- 价格 -->
                    <div class="flex items-center justify-between">
                      <div class="text-lg font-bold text-orange-600">
                        ¥{{ product.price }}
                      </div>
                      <button class="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition-colors">
                        加入购物车
                      </button>
                    </div>
                  </div>
                </NuxtLink>
              </div>
            </div>

            <!-- 加载更多 -->
            <div class="text-center mt-8">
              <button class="bg-gray-200 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-300 transition-colors">
                加载更多商品
              </button>
            </div>
          </main>
        </div>
      </div>
    </section>

    <!-- 宠物护理小贴士 -->
    <section class="py-12 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-2xl font-bold text-center mb-8">🏥 宠物护理小贴士</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="bg-white p-6 rounded-lg shadow-sm">
            <div class="text-3xl mb-4">🍎</div>
            <h3 class="font-semibold mb-2">营养均衡</h3>
            <p class="text-gray-600 text-sm">
              为宠物选择营养均衡的食物，根据年龄和体重合理搭配
            </p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-sm">
            <div class="text-3xl mb-4">🏃</div>
            <h3 class="font-semibold mb-2">适量运动</h3>
            <p class="text-gray-600 text-sm">
              每天保证足够的运动时间，有助于宠物身心健康
            </p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-sm">
            <div class="text-3xl mb-4">💊</div>
            <h3 class="font-semibold mb-2">定期体检</h3>
            <p class="text-gray-600 text-sm">
              定期带宠物进行健康检查，及时发现和预防疾病
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
