<script setup lang="ts">
// REST API 测试页面 - 作为 GraphQL 的临时替代方案
const testResult = ref('');
const isLoading = ref(false);

// 测试 WordPress REST API
const testRestAPI = async () => {
  isLoading.value = true;
  testResult.value = '正在测试 WordPress REST API...';
  
  try {
    const response = await fetch('http://localhost:8080/wp-json/wp/v2/posts?per_page=3');
    
    if (response.ok) {
      const data = await response.json();
      testResult.value = `✅ REST API 连接成功！\n\n文章数据：\n${JSON.stringify(data.map(post => ({
        id: post.id,
        title: post.title.rendered,
        excerpt: post.excerpt.rendered.replace(/<[^>]*>/g, '').substring(0, 100) + '...'
      })), null, 2)}`;
    } else {
      testResult.value = `❌ REST API 连接失败！\n状态码: ${response.status}`;
    }
  } catch (error: any) {
    testResult.value = `❌ REST API 连接错误：\n${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 测试 WooCommerce REST API
const testWooCommerceAPI = async () => {
  isLoading.value = true;
  testResult.value = '正在测试 WooCommerce REST API...';
  
  try {
    const response = await fetch('http://localhost:8080/wp-json/wc/v3/products?per_page=3');
    
    if (response.ok) {
      const data = await response.json();
      testResult.value = `✅ WooCommerce API 连接成功！\n\n产品数据：\n${JSON.stringify(data.map(product => ({
        id: product.id,
        name: product.name,
        price: product.price,
        status: product.status
      })), null, 2)}`;
    } else {
      testResult.value = `❌ WooCommerce API 连接失败！\n状态码: ${response.status}`;
    }
  } catch (error: any) {
    testResult.value = `❌ WooCommerce API 连接错误：\n${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 测试站点信息
const testSiteInfo = async () => {
  isLoading.value = true;
  testResult.value = '正在获取站点信息...';
  
  try {
    const response = await fetch('http://localhost:8080/wp-json/');
    
    if (response.ok) {
      const data = await response.json();
      testResult.value = `✅ 站点信息获取成功！\n\n站点数据：\n${JSON.stringify({
        name: data.name,
        description: data.description,
        url: data.url,
        namespaces: data.namespaces
      }, null, 2)}`;
    } else {
      testResult.value = `❌ 站点信息获取失败！\n状态码: ${response.status}`;
    }
  } catch (error: any) {
    testResult.value = `❌ 站点信息获取错误：\n${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 页面加载时自动测试
onMounted(() => {
  testSiteInfo();
});
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">🔧 WordPress REST API 测试</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
      <button
        @click="testSiteInfo"
        :disabled="isLoading"
        class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 disabled:opacity-50"
      >
        {{ isLoading ? '测试中...' : '测试站点信息' }}
      </button>
      
      <button
        @click="testRestAPI"
        :disabled="isLoading"
        class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 disabled:opacity-50"
      >
        {{ isLoading ? '测试中...' : '测试文章 API' }}
      </button>
      
      <button
        @click="testWooCommerceAPI"
        :disabled="isLoading"
        class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 disabled:opacity-50"
      >
        {{ isLoading ? '测试中...' : '测试产品 API' }}
      </button>
    </div>
    
    <div class="bg-gray-100 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">测试结果：</h2>
      <pre class="whitespace-pre-wrap text-sm bg-white p-4 rounded border overflow-auto max-h-96">{{ testResult || '点击上方按钮开始测试...' }}</pre>
    </div>
    
    <div class="mt-8 bg-blue-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">📋 API 配置信息</h2>
      <div class="space-y-2 text-sm">
        <p><strong>前端地址：</strong> http://localhost:3000</p>
        <p><strong>后端地址：</strong> http://localhost:8080</p>
        <p><strong>WordPress REST API：</strong> http://localhost:8080/wp-json/wp/v2/</p>
        <p><strong>WooCommerce REST API：</strong> http://localhost:8080/wp-json/wc/v3/</p>
        <p><strong>GraphQL 端点：</strong> http://localhost:8080/graphql (需要激活插件)</p>
      </div>
    </div>
    
    <div class="mt-8 bg-yellow-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">🛠️ GraphQL 问题解决方案</h2>
      <div class="space-y-2 text-sm">
        <p><strong>当前状态：</strong> GraphQL 插件可能未正确激活</p>
        <p><strong>临时解决方案：</strong> 使用 WordPress REST API 和 WooCommerce REST API</p>
        
        <p class="mt-4"><strong>激活 GraphQL 插件的步骤：</strong></p>
        <ol class="list-decimal list-inside ml-4 space-y-1">
          <li>访问 WordPress 管理后台：<a href="http://localhost:8080/wp-admin" target="_blank" class="text-blue-600 underline">http://localhost:8080/wp-admin</a></li>
          <li>进入 插件 > 已安装的插件</li>
          <li>找到 "WPGraphQL" 插件并点击"激活"</li>
          <li>同样激活 "WPGraphQL for WooCommerce" 插件</li>
          <li>激活 "WPGraphQL JWT Authentication" 插件</li>
        </ol>
        
        <p class="mt-4"><strong>验证 GraphQL 是否工作：</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li>访问：<a href="http://localhost:8080/graphql" target="_blank" class="text-blue-600 underline">http://localhost:8080/graphql</a></li>
          <li>应该看到 GraphQL 查询界面或相关响应</li>
        </ul>
      </div>
    </div>
    
    <div class="mt-8 bg-green-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">🚀 使用 REST API 的优势</h2>
      <div class="space-y-2 text-sm">
        <ul class="list-disc list-inside space-y-1">
          <li><strong>稳定性：</strong> WordPress REST API 是核心功能，非常稳定</li>
          <li><strong>兼容性：</strong> 所有 WordPress 和 WooCommerce 功能都支持</li>
          <li><strong>文档完善：</strong> 有详细的官方文档和示例</li>
          <li><strong>无需额外插件：</strong> 开箱即用，不依赖第三方插件</li>
          <li><strong>缓存友好：</strong> 更容易实现缓存策略</li>
        </ul>
      </div>
    </div>
    
    <div class="mt-8 text-center">
      <NuxtLink to="/" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 mr-4">
        返回首页
      </NuxtLink>
      <NuxtLink to="/test-cors" class="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600">
        GraphQL 测试
      </NuxtLink>
    </div>
  </div>
</template>
