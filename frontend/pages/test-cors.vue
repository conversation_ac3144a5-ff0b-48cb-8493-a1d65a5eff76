<script setup lang="ts">
// CORS 测试页面
const testResult = ref('');
const isLoading = ref(false);

// 测试 GraphQL 连接
const testGraphQL = async () => {
  isLoading.value = true;
  testResult.value = '正在测试 GraphQL 连接...';
  
  try {
    const query = `
      query TestQuery {
        __schema {
          queryType {
            name
          }
        }
      }
    `;
    
    const response = await fetch('http://localhost:8080/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      },
      credentials: 'include',
      body: JSON.stringify({ query })
    });
    
    if (response.ok) {
      const data = await response.json();
      testResult.value = `✅ GraphQL 连接成功！\n\n响应数据：\n${JSON.stringify(data, null, 2)}`;
    } else {
      testResult.value = `❌ GraphQL 连接失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
    }
  } catch (error: any) {
    testResult.value = `❌ GraphQL 连接错误：\n${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 测试产品查询
const testProducts = async () => {
  isLoading.value = true;
  testResult.value = '正在测试产品查询...';
  
  try {
    const query = `
      query GetProducts {
        products(first: 3) {
          nodes {
            id
            name
            slug
            price
          }
        }
      }
    `;
    
    const response = await fetch('http://localhost:8080/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      },
      credentials: 'include',
      body: JSON.stringify({ query })
    });
    
    if (response.ok) {
      const data = await response.json();
      testResult.value = `✅ 产品查询成功！\n\n产品数据：\n${JSON.stringify(data, null, 2)}`;
    } else {
      testResult.value = `❌ 产品查询失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
    }
  } catch (error: any) {
    testResult.value = `❌ 产品查询错误：\n${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 测试购物车查询
const testCart = async () => {
  isLoading.value = true;
  testResult.value = '正在测试购物车查询...';
  
  try {
    const query = `
      query GetCart {
        cart {
          contents {
            itemCount
          }
          total
        }
      }
    `;
    
    const response = await fetch('http://localhost:8080/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      },
      credentials: 'include',
      body: JSON.stringify({ query })
    });
    
    if (response.ok) {
      const data = await response.json();
      testResult.value = `✅ 购物车查询成功！\n\n购物车数据：\n${JSON.stringify(data, null, 2)}`;
    } else {
      testResult.value = `❌ 购物车查询失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
    }
  } catch (error: any) {
    testResult.value = `❌ 购物车查询错误：\n${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

// 页面加载时自动测试
onMounted(() => {
  testGraphQL();
});
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">🔧 CORS 和 GraphQL 连接测试</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
      <button
        @click="testGraphQL"
        :disabled="isLoading"
        class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 disabled:opacity-50"
      >
        {{ isLoading ? '测试中...' : '测试 GraphQL 连接' }}
      </button>
      
      <button
        @click="testProducts"
        :disabled="isLoading"
        class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 disabled:opacity-50"
      >
        {{ isLoading ? '测试中...' : '测试产品查询' }}
      </button>
      
      <button
        @click="testCart"
        :disabled="isLoading"
        class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 disabled:opacity-50"
      >
        {{ isLoading ? '测试中...' : '测试购物车查询' }}
      </button>
    </div>
    
    <div class="bg-gray-100 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">测试结果：</h2>
      <pre class="whitespace-pre-wrap text-sm bg-white p-4 rounded border overflow-auto max-h-96">{{ testResult || '点击上方按钮开始测试...' }}</pre>
    </div>
    
    <div class="mt-8 bg-blue-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">📋 CORS 配置信息</h2>
      <div class="space-y-2 text-sm">
        <p><strong>前端地址：</strong> http://localhost:3000</p>
        <p><strong>后端地址：</strong> http://localhost:8080</p>
        <p><strong>GraphQL 端点：</strong> http://localhost:8080/graphql</p>
        <p><strong>CORS 策略：</strong> 允许来自前端的跨域请求</p>
        <p><strong>Credentials：</strong> 支持 cookies 和认证信息</p>
      </div>
    </div>
    
    <div class="mt-8 bg-yellow-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">🛠️ 故障排除</h2>
      <div class="space-y-2 text-sm">
        <p><strong>如果看到 CORS 错误：</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li>检查 Nginx 配置是否正确重启</li>
          <li>确认 WordPress 插件已激活</li>
          <li>检查浏览器开发者工具的网络面板</li>
          <li>尝试清除浏览器缓存</li>
        </ul>
        
        <p class="mt-4"><strong>如果看到 GraphQL 错误：</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li>确认 WPGraphQL 插件已安装并激活</li>
          <li>检查 WordPress 是否正常运行</li>
          <li>验证数据库连接是否正常</li>
        </ul>
      </div>
    </div>
    
    <div class="mt-8 text-center">
      <NuxtLink to="/" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600">
        返回首页
      </NuxtLink>
    </div>
  </div>
</template>
