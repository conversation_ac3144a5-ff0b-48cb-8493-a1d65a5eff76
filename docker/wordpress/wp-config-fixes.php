<?php
/**
 * WordPress 配置修复文件
 * 解决 WooCommerce 和 WordPress 6.6+ 的兼容性问题
 */

// 禁用调试模式以避免通知显示
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', false);
define('WP_DEBUG_DISPLAY', false);

// 错误报告级别设置
error_reporting(0);
ini_set('display_errors', 0);

// 输出缓冲修复
if (!headers_sent()) {
    ob_start();
}

// 修复 WooCommerce 翻译加载问题
add_action('plugins_loaded', function() {
    if (class_exists('WooCommerce')) {
        // 移除早期的翻译加载
        remove_action('init', array('WC_Install', 'check_version'), 5);

        // 在正确的时机重新加载
        add_action('init', function() {
            if (function_exists('load_plugin_textdomain')) {
                load_plugin_textdomain('woocommerce', false, dirname(plugin_basename(WC_PLUGIN_FILE)) . '/i18n/languages/');
            }
        }, 15);
    }
}, 1);

// 修复头部信息问题
add_action('init', function() {
    if (!headers_sent()) {
        header('Content-Type: text/html; charset=UTF-8');
    }
}, 1);

// 禁用不必要的通知
add_filter('wp_doing_ajax', function($wp_doing_ajax) {
    if (is_admin() && !$wp_doing_ajax) {
        return false;
    }
    return $wp_doing_ajax;
});

// 清理输出缓冲
add_action('wp_loaded', function() {
    if (ob_get_level()) {
        ob_end_clean();
    }
});

// CORS 修复 - 允许 woocommerce-session 头部
add_action('init', function() {
    // 处理 CORS 预检请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
        $allowed_origins = [
            'http://localhost:3000',
            'http://127.0.0.1:3000',
            getenv('FRONTEND_URL') ?: 'http://localhost:3000'
        ];

        if (in_array($origin, $allowed_origins)) {
            header('Access-Control-Allow-Origin: ' . $origin);
            header('Access-Control-Allow-Credentials: true');
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, woocommerce-session, Origin');
            header('Access-Control-Max-Age: 86400');

            // 对于预检请求，返回 200 状态码并退出
            http_response_code(200);
            exit();
        }
    }

    // 为所有请求添加 CORS 头部
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    $allowed_origins = [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        getenv('FRONTEND_URL') ?: 'http://localhost:3000'
    ];

    if (in_array($origin, $allowed_origins)) {
        header('Access-Control-Allow-Origin: ' . $origin);
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Expose-Headers: Content-Type, Authorization, X-Requested-With, woocommerce-session');
    }
}, 1);

// 为 GraphQL 端点添加额外的 CORS 支持
add_action('graphql_init', function() {
    add_filter('graphql_response_headers_to_send', function($headers) {
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
        $allowed_origins = [
            'http://localhost:3000',
            'http://127.0.0.1:3000',
            getenv('FRONTEND_URL') ?: 'http://localhost:3000'
        ];

        if (in_array($origin, $allowed_origins)) {
            $headers['Access-Control-Allow-Origin'] = $origin;
            $headers['Access-Control-Allow-Credentials'] = 'true';
            $headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, woocommerce-session, Origin';
            $headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS, PUT, DELETE';
        }

        return $headers;
    });
});
?>
