# 🎨 WooNuxt 二次开发完整指南

## 📖 项目架构概述

WooNuxt 采用了 **Nuxt Layers** 架构，这是一种类似于 WordPress 父子主题的设计模式：

- **`woonuxt_base/`** - 父主题（核心功能）
- **项目根目录** - 子主题（自定义扩展）

### 🏗️ 目录结构

```
frontend/
├── woonuxt_base/              # 核心主题（不要直接修改）
│   ├── app/
│   │   ├── components/        # 基础组件
│   │   ├── pages/            # 基础页面
│   │   ├── composables/      # 组合式函数
│   │   ├── queries/          # GraphQL 查询
│   │   ├── types/            # TypeScript 类型
│   │   └── locales/          # 多语言文件
│   └── nuxt.config.ts        # 核心配置
├── components/               # 自定义组件（覆盖基础组件）
├── pages/                    # 自定义页面（覆盖基础页面）
├── composables/              # 自定义组合式函数
├── public/                   # 静态资源
└── nuxt.config.ts           # 项目配置（扩展核心配置）
```

## 🎯 二次开发核心原理

### 1. 组件覆盖机制

WooNuxt 使用 Nuxt 的组件自动导入功能，**同名组件会自动覆盖**：

```bash
# 基础组件
woonuxt_base/app/components/ProductCard.vue

# 自定义组件（会覆盖基础组件）
components/ProductCard.vue
```

### 2. 页面覆盖机制

页面遵循相同的覆盖规则：

```bash
# 基础页面
woonuxt_base/app/pages/index.vue

# 自定义页面（会覆盖基础页面）
pages/index.vue
```

### 3. 配置扩展机制

项目配置通过 `extends` 继承核心配置：

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  // 继承核心配置
  extends: ['./woonuxt_base'],
  
  // 自定义配置
  components: [{ path: './components', pathPrefix: false }],
})
```

## 🛠️ 常见二次开发场景

### 1. 自定义首页

创建 `pages/index.vue` 覆盖默认首页：

```vue
<script setup lang="ts">
// 使用现有的 composables
const { data } = await useAsyncGql('getProducts', { first: 8 });
const products = data.value.products?.nodes || [];

// SEO 设置
useSeoMeta({
  title: '宠物用品商城',
  description: '专业的宠物用品电商平台'
});
</script>

<template>
  <main>
    <!-- 自定义首页内容 -->
    <section class="hero-section">
      <h1>欢迎来到宠物用品商城</h1>
    </section>
    
    <!-- 复用现有组件 -->
    <ProductGrid :products="products" />
  </main>
</template>
```

### 2. 自定义产品卡片

创建 `components/productElements/ProductCard.vue`：

```vue
<script setup lang="ts">
// 继承原有 props
const props = defineProps({
  node: { type: Object as PropType<Product>, required: true },
  index: { type: Number, default: 1 },
});

// 添加自定义逻辑
const isPetProduct = computed(() => {
  return props.node.productCategories?.nodes.some(
    cat => cat.slug?.includes('pet')
  );
});
</script>

<template>
  <div class="custom-product-card">
    <!-- 宠物产品特殊标识 -->
    <div v-if="isPetProduct" class="pet-badge">
      🐾 宠物专用
    </div>
    
    <!-- 复用原有结构，添加自定义样式 -->
    <NuxtLink :to="`/product/${node.slug}`">
      <NuxtImg :src="node.image?.sourceUrl" :alt="node.name" />
      <h3>{{ node.name }}</h3>
      <ProductPrice :node="node" />
    </NuxtLink>
  </div>
</template>

<style scoped>
.custom-product-card {
  /* 自定义样式 */
}
</style>
```

### 3. 创建新页面

创建 `pages/about.vue`：

```vue
<script setup lang="ts">
useSeoMeta({
  title: '关于我们 - 宠物用品商城',
  description: '了解我们的宠物用品商城'
});
</script>

<template>
  <div class="container mx-auto py-8">
    <h1>关于我们</h1>
    <p>我们是专业的宠物用品供应商...</p>
  </div>
</template>
```

### 4. 自定义 Composable

创建 `composables/usePetCategories.ts`：

```typescript
export function usePetCategories() {
  const petCategories = useState<ProductCategory[]>('petCategories', () => []);
  
  async function fetchPetCategories() {
    const { data } = await useAsyncGql('getProductCategories', {
      where: { slug: ['dog-supplies', 'cat-supplies', 'bird-supplies'] }
    });
    
    petCategories.value = data.value?.productCategories?.nodes || [];
    return petCategories.value;
  }
  
  return {
    petCategories: readonly(petCategories),
    fetchPetCategories
  };
}
```

## 🎨 样式自定义

### 1. Tailwind CSS 配置

修改 `tailwind.config.ts`：

```typescript
export default {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#fef7ee',
          500: '#f97316',  // 宠物主题橙色
          900: '#9a3412',
        },
        pet: {
          dog: '#8B4513',    // 狗狗棕色
          cat: '#4A5568',    // 猫咪灰色
          bird: '#38B2AC',   // 鸟儿青色
        }
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      }
    }
  }
}
```

### 2. 全局样式

创建 `assets/css/custom.css`：

```css
/* 宠物主题样式 */
.pet-theme {
  --primary-color: #f97316;
  --secondary-color: #8B4513;
}

/* 自定义组件样式 */
.product-card-pet {
  border: 2px solid var(--primary-color);
  border-radius: 12px;
}
```

## 🔌 插件和模块扩展

### 1. 添加自定义插件

创建 `plugins/pet-analytics.client.ts`：

```typescript
export default defineNuxtPlugin(() => {
  // 宠物产品分析插件
  const trackPetProductView = (productId: string, category: string) => {
    // 自定义分析逻辑
    console.log(`Pet product viewed: ${productId} in ${category}`);
  };
  
  return {
    provide: {
      petAnalytics: {
        trackPetProductView
      }
    }
  };
});
```

### 2. 扩展 Nuxt 配置

在 `nuxt.config.ts` 中添加：

```typescript
export default defineNuxtConfig({
  extends: ['./woonuxt_base'],
  
  // 添加自定义模块
  modules: [
    '@nuxtjs/google-analytics',  // 分析
    '@nuxtjs/sitemap',          // SEO
  ],
  
  // 自定义配置
  googleAnalytics: {
    id: 'GA_MEASUREMENT_ID'
  },
  
  // 运行时配置
  runtimeConfig: {
    public: {
      petStoreApiKey: process.env.PET_STORE_API_KEY
    }
  }
});
```

## 🌍 多语言自定义

### 1. 添加自定义翻译

创建 `locales/zh-CN.json`：

```json
{
  "messages": {
    "pet": {
      "categories": {
        "dog": "狗狗用品",
        "cat": "猫咪用品",
        "bird": "鸟类用品",
        "fish": "鱼类用品"
      },
      "features": {
        "petFriendly": "宠物友好",
        "organic": "有机天然",
        "veterinaryApproved": "兽医推荐"
      }
    }
  }
}
```

### 2. 在组件中使用

```vue
<template>
  <div>
    <h2>{{ $t('messages.pet.categories.dog') }}</h2>
    <span class="badge">{{ $t('messages.pet.features.organic') }}</span>
  </div>
</template>
```

## 🔍 GraphQL 查询自定义

### 1. 创建自定义查询

创建 `queries/getPetProducts.gql`：

```graphql
query GetPetProducts($first: Int, $category: String) {
  products(
    first: $first
    where: { categoryIn: [$category] }
  ) {
    nodes {
      id
      name
      slug
      image {
        sourceUrl
        altText
      }
      ... on SimpleProduct {
        price
        regularPrice
        salePrice
      }
      productCategories {
        nodes {
          name
          slug
        }
      }
    }
  }
}
```

### 2. 在组件中使用

```vue
<script setup lang="ts">
const { data } = await useAsyncGql('getPetProducts', {
  first: 12,
  category: 'dog-supplies'
});

const dogProducts = data.value?.products?.nodes || [];
</script>
```

## 🚀 性能优化

### 1. 图片优化

```vue
<template>
  <!-- 使用 Nuxt Image 优化 -->
  <NuxtImg
    :src="product.image?.sourceUrl"
    :alt="product.name"
    width="300"
    height="300"
    format="webp"
    quality="80"
    loading="lazy"
  />
</template>
```

### 2. 代码分割

```typescript
// 动态导入大型组件
const PetProductFilter = defineAsyncComponent(
  () => import('~/components/PetProductFilter.vue')
);
```

## 📱 移动端适配

### 1. 响应式组件

```vue
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <!-- 移动端 1 列，平板 2 列，桌面 4 列 -->
    <ProductCard v-for="product in products" :key="product.id" :node="product" />
  </div>
</template>
```

### 2. 移动端特定功能

```vue
<script setup lang="ts">
const isMobile = useMediaQuery('(max-width: 768px)');

const showMobileMenu = ref(false);
</script>

<template>
  <div>
    <!-- 移动端菜单 -->
    <button v-if="isMobile" @click="showMobileMenu = true">
      菜单
    </button>
  </div>
</template>
```

## 🧪 测试和调试

### 1. 开发工具

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 2. 调试技巧

```vue
<script setup lang="ts">
// 开发环境调试
if (process.dev) {
  console.log('Product data:', product);
}

// 使用 Vue DevTools
const debugInfo = computed(() => ({
  productCount: products.value.length,
  currentCategory: route.params.category
}));
</script>
```

## 📦 部署和发布

### 1. 环境变量配置

```bash
# .env
GQL_HOST=https://your-wordpress-site.com/graphql
APP_HOST=https://your-frontend-domain.com
NUXT_IMAGE_DOMAINS=your-wordpress-site.com
```

### 2. 构建优化

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  nitro: {
    prerender: {
      routes: ['/sitemap.xml']
    }
  },
  
  // 生产环境优化
  experimental: {
    payloadExtraction: false
  }
});
```

---

## 🎉 总结

WooNuxt 的二次开发核心在于：

1. **理解 Layers 架构** - 父子主题模式
2. **组件覆盖机制** - 同名组件自动覆盖
3. **合理使用 Composables** - 复用业务逻辑
4. **GraphQL 查询优化** - 按需获取数据
5. **样式系统扩展** - Tailwind CSS 自定义

通过这种方式，你可以在不修改核心代码的情况下，完全自定义你的宠物电商网站！

## 🎯 实战示例：宠物电商定制

### 1. 创建宠物分类导航

创建 `components/PetCategoryNav.vue`：

```vue
<script setup lang="ts">
const petCategories = [
  { name: '狗狗用品', slug: 'dog-supplies', icon: '🐕', color: 'bg-amber-100' },
  { name: '猫咪用品', slug: 'cat-supplies', icon: '🐱', color: 'bg-gray-100' },
  { name: '鸟类用品', slug: 'bird-supplies', icon: '🐦', color: 'bg-blue-100' },
  { name: '鱼类用品', slug: 'fish-supplies', icon: '🐠', color: 'bg-cyan-100' },
];
</script>

<template>
  <nav class="pet-category-nav">
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <NuxtLink
        v-for="category in petCategories"
        :key="category.slug"
        :to="`/product-category/${category.slug}`"
        :class="[category.color, 'p-6 rounded-lg text-center hover:shadow-lg transition-shadow']"
      >
        <div class="text-4xl mb-2">{{ category.icon }}</div>
        <h3 class="font-semibold">{{ category.name }}</h3>
      </NuxtLink>
    </div>
  </nav>
</template>
```

### 2. 宠物年龄筛选组件

创建 `components/filters/PetAgeFilter.vue`：

```vue
<script setup lang="ts">
const emit = defineEmits<{
  'update:modelValue': [value: string[]]
}>();

const props = defineProps<{
  modelValue: string[]
}>();

const ageGroups = [
  { label: '幼宠 (0-1岁)', value: 'puppy-kitten' },
  { label: '成年 (1-7岁)', value: 'adult' },
  { label: '老年 (7岁+)', value: 'senior' },
];

const selectedAges = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});
</script>

<template>
  <div class="pet-age-filter">
    <h4 class="font-semibold mb-3">适用年龄</h4>
    <div class="space-y-2">
      <label
        v-for="age in ageGroups"
        :key="age.value"
        class="flex items-center space-x-2 cursor-pointer"
      >
        <input
          v-model="selectedAges"
          type="checkbox"
          :value="age.value"
          class="rounded border-gray-300"
        />
        <span>{{ age.label }}</span>
      </label>
    </div>
  </div>
</template>
```

### 3. 宠物品种选择器

创建 `composables/usePetBreeds.ts`：

```typescript
export function usePetBreeds() {
  const dogBreeds = [
    '金毛寻回犬', '拉布拉多', '德国牧羊犬', '边境牧羊犬',
    '泰迪', '比熊', '柯基', '哈士奇'
  ];

  const catBreeds = [
    '英国短毛猫', '美国短毛猫', '波斯猫', '暹罗猫',
    '布偶猫', '缅因猫', '苏格兰折耳猫', '俄罗斯蓝猫'
  ];

  const getBreedsByPetType = (petType: 'dog' | 'cat') => {
    return petType === 'dog' ? dogBreeds : catBreeds;
  };

  const searchBreeds = (query: string, petType: 'dog' | 'cat') => {
    const breeds = getBreedsByPetType(petType);
    return breeds.filter(breed =>
      breed.toLowerCase().includes(query.toLowerCase())
    );
  };

  return {
    dogBreeds,
    catBreeds,
    getBreedsByPetType,
    searchBreeds
  };
}
```

## 🛒 购物车定制

### 1. 宠物信息收集

创建 `components/cart/PetInfoForm.vue`：

```vue
<script setup lang="ts">
const petInfo = ref({
  name: '',
  type: '',
  breed: '',
  age: '',
  weight: '',
  allergies: []
});

const { addPetInfoToCart } = useCart();

const savePetInfo = () => {
  addPetInfoToCart(petInfo.value);
};
</script>

<template>
  <div class="pet-info-form bg-blue-50 p-6 rounded-lg">
    <h3 class="text-lg font-semibold mb-4">🐾 宠物信息</h3>
    <p class="text-sm text-gray-600 mb-4">
      为了给您推荐最适合的产品，请填写宠物信息
    </p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium mb-1">宠物姓名</label>
        <input
          v-model="petInfo.name"
          type="text"
          class="w-full p-2 border rounded-md"
          placeholder="例如：小白"
        />
      </div>

      <div>
        <label class="block text-sm font-medium mb-1">宠物类型</label>
        <select v-model="petInfo.type" class="w-full p-2 border rounded-md">
          <option value="">请选择</option>
          <option value="dog">狗狗</option>
          <option value="cat">猫咪</option>
          <option value="bird">鸟类</option>
          <option value="fish">鱼类</option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium mb-1">年龄</label>
        <input
          v-model="petInfo.age"
          type="text"
          class="w-full p-2 border rounded-md"
          placeholder="例如：2岁"
        />
      </div>

      <div>
        <label class="block text-sm font-medium mb-1">体重</label>
        <input
          v-model="petInfo.weight"
          type="text"
          class="w-full p-2 border rounded-md"
          placeholder="例如：5kg"
        />
      </div>
    </div>

    <button
      @click="savePetInfo"
      class="mt-4 bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-600"
    >
      保存宠物信息
    </button>
  </div>
</template>
```

## 📊 数据分析和追踪

### 1. 宠物产品分析

创建 `composables/usePetAnalytics.ts`：

```typescript
export function usePetAnalytics() {
  const trackPetProductView = (product: Product, petType?: string) => {
    // 发送分析数据
    $fetch('/api/analytics/product-view', {
      method: 'POST',
      body: {
        productId: product.id,
        productName: product.name,
        petType,
        timestamp: new Date().toISOString(),
        category: product.productCategories?.nodes[0]?.slug
      }
    });
  };

  const trackPetCategoryInterest = (category: string, petType: string) => {
    // 追踪用户对特定宠物类别的兴趣
    $fetch('/api/analytics/category-interest', {
      method: 'POST',
      body: {
        category,
        petType,
        timestamp: new Date().toISOString()
      }
    });
  };

  return {
    trackPetProductView,
    trackPetCategoryInterest
  };
}
```

## 🎨 主题定制系统

### 1. 动态主题切换

创建 `composables/usePetTheme.ts`：

```typescript
export function usePetTheme() {
  const currentTheme = useState<'dog' | 'cat' | 'bird' | 'fish'>('petTheme', () => 'dog');

  const themes = {
    dog: {
      primary: '#D97706',
      secondary: '#92400E',
      accent: '#FED7AA',
      background: '#FFF7ED'
    },
    cat: {
      primary: '#6B7280',
      secondary: '#374151',
      accent: '#E5E7EB',
      background: '#F9FAFB'
    },
    bird: {
      primary: '#0891B2',
      secondary: '#0E7490',
      accent: '#A7F3D0',
      background: '#ECFDF5'
    },
    fish: {
      primary: '#0D9488',
      secondary: '#0F766E',
      accent: '#99F6E4',
      background: '#F0FDFA'
    }
  };

  const setTheme = (theme: keyof typeof themes) => {
    currentTheme.value = theme;

    // 更新 CSS 变量
    const root = document.documentElement;
    const colors = themes[theme];

    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
  };

  return {
    currentTheme: readonly(currentTheme),
    themes,
    setTheme
  };
}
```

## 🔔 通知系统

### 1. 宠物护理提醒

创建 `components/notifications/PetCareReminder.vue`：

```vue
<script setup lang="ts">
const { user } = useAuth();
const reminders = ref([
  {
    id: 1,
    type: 'vaccination',
    petName: '小白',
    message: '疫苗接种提醒：小白的疫苗将在3天后到期',
    dueDate: '2024-01-15',
    priority: 'high'
  },
  {
    id: 2,
    type: 'grooming',
    petName: '小黑',
    message: '美容提醒：小黑已经6周没有美容了',
    dueDate: '2024-01-10',
    priority: 'medium'
  }
]);

const dismissReminder = (id: number) => {
  reminders.value = reminders.value.filter(r => r.id !== id);
};
</script>

<template>
  <div v-if="reminders.length" class="pet-care-reminders">
    <h3 class="text-lg font-semibold mb-4">🔔 宠物护理提醒</h3>

    <div class="space-y-3">
      <div
        v-for="reminder in reminders"
        :key="reminder.id"
        :class="[
          'p-4 rounded-lg border-l-4',
          reminder.priority === 'high' ? 'border-red-500 bg-red-50' :
          reminder.priority === 'medium' ? 'border-yellow-500 bg-yellow-50' :
          'border-blue-500 bg-blue-50'
        ]"
      >
        <div class="flex justify-between items-start">
          <div>
            <p class="font-medium">{{ reminder.message }}</p>
            <p class="text-sm text-gray-600 mt-1">
              到期时间: {{ new Date(reminder.dueDate).toLocaleDateString() }}
            </p>
          </div>
          <button
            @click="dismissReminder(reminder.id)"
            class="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 🎯 最佳实践总结

### 1. 开发流程
1. **先理解基础组件** - 查看 `woonuxt_base/app/components`
2. **创建覆盖组件** - 在项目根目录创建同名组件
3. **渐进式定制** - 先复制基础组件，再逐步修改
4. **测试兼容性** - 确保自定义组件与现有系统兼容

### 2. 性能优化
- 使用 `defineAsyncComponent` 懒加载大型组件
- 合理使用 `useState` 管理全局状态
- 优化 GraphQL 查询，避免过度获取数据
- 使用 Nuxt Image 优化图片加载

### 3. 代码组织
- 按功能模块组织组件 (`components/pet/`, `components/cart/`)
- 使用 TypeScript 提供类型安全
- 创建可复用的 Composables
- 保持组件单一职责

### 4. 用户体验
- 响应式设计，支持移动端
- 加载状态和错误处理
- 无障碍访问支持
- 国际化和本地化

通过遵循这些原则和示例，你可以高效地定制出专业的宠物电商网站！
